import Vue from 'vue'
import { Dropdown, DropdownMenu, DropdownItem, Icon, Form, FormItem, Input, Message, Button,Row,Col,Select,Option } from 'view-design'

import 'view-design/dist/styles/iview.css'
Vue.prototype.$Message = Message
Vue.component('Dropdown', Dropdown);
Vue.component('DropdownMenu', DropdownMenu);
Vue.component('DropdownItem', DropdownItem);
Vue.component('Icon', Icon);
Vue.component('Form', Form)
Vue.component('FormItem', FormItem)
Vue.component('Input', Input)
Vue.component('Message', Message)
Vue.component('Button', Button)
Vue.component('Row', Row)
Vue.component('Col', Col)
Vue.component('Select', Select)
Vue.component('Option', Option)



