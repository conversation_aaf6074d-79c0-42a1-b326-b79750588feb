<template>
    <div class = "MobileCarousel">
        <div class = "MobileCarouselItem">
        </div>
    </div>
</template>

<script>
    export default {
        name: "MobileCarousel",
    }
</script>
<style lang="less" scoped>
    .MobileCarousel{
        height:1.84rem;
        width:100%;
        &Item{
            width:100%;
            height:1.84rem;
            background:url("../../assets/images/manageBk.png") no-repeat center;
            background-size: cover;
        }
    }
</style>
