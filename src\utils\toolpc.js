// 这是封装的公共的方法
import Vue from 'vue'

const Tool = {
    // 动态获取接口域名
    linkUrl() {
        return process.env.VUE_APP_BASEURL
    },
    /**
     * @param {}
     * @description 获取token
     */
    getToken() {
        return sessionStorage.getItem('_token')
    },

    /**
     *
     * @param {String} key
     * @param {String} val
     * @description 统一设置sessionStorage
     */
    setItem(key, val) {
        if (typeof val === 'object') {
            sessionStorage.setItem(key, JSON.stringify(val))
        } else {
            sessionStorage.setItem(key, val)
        }
    },

    /**
     *
     * @param {String} key
     * @description 统一获取sessionStorage
     */
    getItem(key) {
        let ele = sessionStorage.getItem(key)
        let val = null
        try {
            val = JSON.parse(ele)
        } catch (error) {
            val = ele
        }
        return val
    },

    /**
     *
     * @param {String} key
     * @param {String} val
     * @description 统一设置localStorage
     */
    setLocalItem(key, val) {
        if (typeof val === 'object') {
            localStorage.setItem(key, JSON.stringify(val))
        } else {
            localStorage.setItem(key, val)
        }
    },

    /**
     *
     * @param {String} key
     * @description 统一获取localStorage
     */
    getLocalItem(key) {
        let ele = localStorage.getItem(key)
        let val = null
        try {
            val = JSON.parse(ele)
        } catch (error) {
            val = ele
        }
        return val
    },
    /*
    * 获取地址栏参数
    * */
    GetUrlParam(paramName) {
        var url = document.URL;
        var oRegex = new RegExp('[\?&]' + paramName + '=([^&]+)', 'i');
        //var oMatch = oRegex.exec( window.top.location.search ) ;
        var oMatch = oRegex.exec(url);
        if (oMatch && oMatch.length > 1)
            return oMatch[1]; //返回值
        else
            return '';
    },

    /**
     *
     * @param {Number} num
     * @description 小于10的数前面用0补齐
     */
    getHandledValue(num) {
        return num < 10 ? '0' + num : num
    },
    /**
     *
     * @param { string | number } timeStamp 传入的时间戳
     * @param { string } startType  要返回的时间字符串的格式类型,传入year则返回年开头的完整时间
     */
    getDate(timeStamp, startType) {
        const d = new Date(timeStamp * 1000) // 10位的时间戳 秒
        // const d = new Date() // 13位的时间戳 毫秒
        const year = d.getFullYear()
        const month = this.getHandledValue(d.getMonth() + 1)
        const date = this.getHandledValue(d.getDate())
        const hours = this.getHandledValue(d.getHours())
        const minutes = this.getHandledValue(d.getMinutes())
        const second = this.getHandledValue(d.getSeconds())
        let resStr = ''
        if (startType === 'year') {
            resStr = year + '-' + month + '-' + date + ' ' + hours + ':' + minutes + ':' + second
        } else {
            resStr = month + '-' + date + ' ' + hours + ':' + minutes
        }
        return resStr
    },
    // 判断手机pc
    ifPcAndH5() {
        if (!/windows phone|iphone|android/ig.test(window.navigator.userAgent)) {
            return "pc"
        } else {
            return "h5"
        }
    },
    // 判断苹果喝安卓
    isAndroid() {
        var u = navigator.userAgent, app = navigator.appVersion;
        var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
        var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
        if (isAndroid) {
            return true
        }
        if (isIOS) {
            return false
        }
    },
}
export default Tool
