<template>
  <div class="IPC">
    <div class="banner">
      <img src="../../assets/images/bannerfaqs.png">
    </div>
    <div class="w">
      <div class="IPC-h poas">
       {{irc.h}}
      </div>
      <div class="IPC-text">
       {{irc.txt}}
      </div>
      
      <div class="IPC-data" v-for="(item,index) in irc.data" :key="index+1">
        <div class="IPC-data-title">{{item.title}}</div>
        <div class="IPC-data-title">{{item.title1}}</div>
        <div class="IPC-data-con">{{item.email}}</div>
        <div class="IPC-data-con">{{item.phone}}</div>
      </div>
    </div>
    
  </div>
</template>

<script>
import { irc } from "@/pagesData/ShareholderServices/pages";
  export default {
    name: 'pcIRContacts',
    data() {
      return {
        irc
      }
    }
  }
</script>

<style lang="less" scoped>
  .IPC{
    padding-bottom: 1rem;
    .banner{
      width: 100%;
      img{
        width: 100%;
      }
    }
    &-h{
      position: relative;
      font-size:0.4rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.52rem;
      letter-spacing:0.02rem;
      margin-top: 0.8rem;
      text-transform: uppercase;
    }
    &-text{
      margin-top: 0.6rem;
      font-size:0.2rem;
      font-family:MicrosoftYaHei;
      color:rgba(34,34,34,1);
      line-height:0.26rem;
      letter-spacing:0.01rem;
    }
    &-data{
      position: relative;
      margin-top: 0.5rem;
      padding-left: 0.27rem;
      &::after{
        content: '';
        position: absolute;
        width: 0.16rem;
        height: 0.16rem;
        top: 0.05rem;
        left: 0.05rem;
        background: url('../../assets/images/iconjt.png');
        background-size:100% 100%;  
      }
      &-title{
        font-size:0.2rem;
        font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
        font-weight:bold;
        color:rgba(34,34,34,1);
        line-height:0.26rem;
        letter-spacing:0.01rem;
        margin-bottom: 0.16rem;
      }
      &-con{
        font-size:0.2rem;
        font-family:MicrosoftYaHei;
        color:rgba(34,34,34,1);
        line-height:0.26rem;
        letter-spacing:0.01rem;
        margin-bottom: 0.16rem;
      }
    }
  }
  .poas::before{
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width:0.6rem;
    height:0.06rem;
    background:rgba(236,177,67,1);
  }
</style>