<template>
  <div class="mobilnew">
    
    <div class="banner">
      <img src="../../assets/images/bannerfaqsip.png" >
    </div>

    <div class="mobilnewH poas mobilnewpd">{{faqs.title}}</div>
    
    <div class="mobilnewpd">
      <div v-for="(item,index) in faqs.data" :key="index+1">
        <faqs-list-ip :item="item"></faqs-list-ip>
      </div>
    </div>
  </div>
</template>

<script>
  import { faqs } from "@/pagesData/ShareholderServices/pages";
  import faqsListIp from "@/components/faqs-list-ip.vue";
  export default {
    data() {
      return {
        faqs,
      }
    },
    components:{
      faqsListIp
    },
  }
</script>

<style lang="less" scoped>
  .mobilnew{
    padding-bottom: 0.3rem;
  }
  .mobilnewpd{
    padding: 0 0.1rem;
  }
  .mobilnewH{
    position: relative;
    font-size:0.18rem;
    font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
    font-weight:bold;
    color:rgba(51,51,51,1);
    line-height:0.24rem;
    letter-spacing:0.01rem;
    margin-top: 0.37rem;
    margin-bottom: 30px;
    text-transform: uppercase;
  }
  .poas::before{
    position: absolute;
    left: 0.1rem;
    top: -0.04rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }
</style>