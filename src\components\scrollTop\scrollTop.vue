<template>
  <div class="returnTop" v-show="returnTop" @click="handleClickReturnTop">
    <img src="../../assets/images/icon_up.png" alt="" />
  </div>
</template>
<script>
export default {
  name: "scrollTop",
  data() {
    return {
      returnTop: false
    };
  },
  mounted() {
    // 此处true需要加上，不加滚动事件可能绑定不成功
    window.addEventListener("scroll", this.handleScroll, true);
  },
  methods: {
    handleScroll() {
      let scrolltop =
        document.documentElement.scrollTop || document.body.scrollTop;
      scrolltop > 300 ? (this.returnTop = true) : (this.returnTop = false);
    },
    handleClickReturnTop() {
      let top = document.documentElement.scrollTop || document.body.scrollTop;
      const timeTop = setInterval(() => {
        document.body.scrollTop = document.documentElement.scrollTop = top -= 50;
        if (top <= 0) {
          clearInterval(timeTop);
        }
      }, 10);
    }
  }
};
</script>
<style lang="less" scoped>
.returnTop {
  width: 0.48rem;
  height: 0.48rem;
  position: fixed;
  right: 0.1rem;
  bottom: 0.3rem;
  z-index: 100;
  cursor: pointer;
  img {
    width: 0.48rem;
    height: 0.48rem;
  }
}
</style>