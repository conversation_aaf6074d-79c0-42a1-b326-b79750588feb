<template>
  <div class="mobilreq">
    
    <div class="banner">
      <img src="../../assets/images/bannerfaqsip.png" >
    </div>
    <div class="mobilreqpd">
      <div class="mobilreqpd-h poas mobilnewpd">{{req.h}}</div>
      <div class="req-txt">{{req.txt}}</div>
      <div class="req-title">{{req.title}}</div>
      <div class="req-formEd">{{req.formEd}}(<i>*</i>).</div>

      <Form class="form" ref="formValidate" :model="formValidate" :rules="ruleValidate" inline label-position='top'>

        <FormItem label="First Name" style="width:100%" prop='first_name'>
          <Input v-model="formValidate.first_name"  :maxlength="50"></Input>
        </FormItem>

        <FormItem label="Last Name" style="width:100%" prop='last_name'>
          <Input v-model="formValidate.last_name"  :maxlength="50"></Input>
        </FormItem>
    
        <FormItem label="Email" style="width:100%" prop='email'>
          <Input v-model="formValidate.email"  ></Input>
        </FormItem>

        <FormItem label="Title" style="width:100%">
          <Input v-model="formValidate.subject"  ></Input>
        </FormItem>

        <FormItem label="Company" style="width:100%">
          <Input v-model="formValidate.company"  ></Input>
        </FormItem>

        <FormItem label="Investor Type" style="width:100%">
          <Select v-model="formValidate.investor_type" placeholder=" ">
            <Option value="1">Individual Investor</Option>
            <Option value="2">Buy-Side Analyst</Option>
            <Option value="3">Sell-Side Analyst</Option>
            <Option value="4">Portfolio Manager</Option>
            <Option value="5">Stock Broker</Option>
            <Option value="6">Employee</Option>
            <Option value="7">News Media</Option>
            <Option value="8">Library</Option>
            <Option value="9">Other</Option>
          </Select>
        </FormItem>

        <FormItem label="Country" style="width:100%">
          <Select v-model="formValidate.country" placeholder=" "  @on-change="changeCountry">
            <Option value="">- None -</Option>
            <Option
              v-for="(item, index) in countryList"
              :value="item.l_lemma_item"
              :key="index + 1"
              >{{ item.vc_content_en }}</Option
            >
          </Select>
        </FormItem>
        
        <FormItem label="Street address" style="width:100%" prop="street_address" v-if="isAddress">
          <Input v-model="formValidate.street_address"  :maxlength="150"></Input>
        </FormItem>

        <FormItem label="Work Phone" style="width:100%">
          <Input v-model="formValidate.work_phone"  ></Input>
        </FormItem>

        <FormItem label="Fax" style="width:100%">
          <Input v-model="formValidate.fax"  ></Input>
        </FormItem>
        
        <FormItem label="Home Phone" style="width:100%">
          <Input v-model="formValidate.home_phone"  ></Input>
        </FormItem>

        <FormItem label="Comments" style="width:100%">
            <Input v-model="formValidate.comments" type="textarea" :autosize="{minRows: 6,maxRows: 10}"></Input>
        </FormItem>

        <div class="form-code" @click="getCaptca()">
          <img style="width:100%" :src="capData.cap" />
        </div>

        <FormItem label="What code is in the image? " style="width:100%" prop="cap">
            <Input v-model="formValidate.cap" :maxlength="4" ></Input>
        </FormItem>

        <div class="form-imgtxt">Enter the characters shown in the image.</div>
        <div class="form-get">Get new captcha!</div>

        <div class="form-submit" @click="handleSubmit('formValidate')">Submit</div>
      </Form>

    </div>

  </div>
</template>

<script>
import { req } from '@/pagesData/ShareholderServices/pages';
import mixin from '@/utils/mixin.js';
  export default {
    mixins:[mixin],
    data() {
      return {
        req,
      }
    },
  }
</script>

<style lang="less" scoped>
  .mobilreqpd{
    padding: 37px 10px 40px;
    &-h{
      position: relative;
      font-size:18px;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:24px;
      letter-spacing:1px;
      text-transform: uppercase;
    }
    .req-txt{
      font-size:13px;
      font-family:MicrosoftYaHei;
      color:rgba(51,51,51,1);
      line-height:18px;
      margin-top: 20px;
    }
    .req-title{
      font-size:18px;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:24px;
      margin-top: 20px;
    }
    .req-formEd{
      font-size:13px;
      font-family:MicrosoftYaHei;
      color:rgba(51,51,51,1);
      line-height:18px;
      margin-top: 16px;
      i{
        color: #E34656;
        font-weight: 700;
      }
    }
    .form{
      margin-top: 0.40rem;
    }
    .form-code{
      width:153px;
      height:60px;
      background:#fff;
      margin-bottom: 26px;
    }
    .form-imgtxt,.form-get{
      font-size:13px;
      font-family:MicrosoftYaHei;
      color:rgba(51,51,51,1);
      line-height:17px;
      margin-bottom: 26px;
    }
    .form-submit{
      width:250px;
      height:50px;
      background:rgba(236,177,67,1);
      outline: none;
      border: none;
      line-height: 50px;
      text-align: center;
      font-size:16px;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(85,58,8,1);
    }
  }


  .poas::before{
    position: absolute;
    left: 0;
    top: -0.04rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }

   /deep/ .ivu-form-item-label{
    font-size:0.16rem;
    font-family:NotoSansHans-Bold,NotoSansHans;
    font-weight:bold;
    color:rgba(51,51,51,1);
    line-height:0.24rem;
  }
  /deep/ .ivu-form-item-error .ivu-input{
      border: 0.01rem solid #E34656 !important;
  }
  /deep/ .ivu-form-item-required .ivu-form-item-label:before{
    display: none;
  }
  /deep/ .ivu-form-item-required .ivu-form-item-label::after{
    content: '*';
    display: inline-block;
    margin-left: 0.04rem;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #E34656;
  }
  /deep/ .ivu-select-selection{
    height: 42px!important;
    border: 1px solid #DDDDDD !important;
    line-height: 42px!important;
  }
  /deep/ .ivu-select-dropdown {
    background: #fff;
  }
  /deep/ .ivu-select-placeholder,/deep/ .ivu-select-selected-value{
    height: 42px!important;
    line-height: 42px!important;
  }
  /deep/ .ivu-icon-ios-arrow-down:before{
    content: "\F33D"!important;
  }
</style>