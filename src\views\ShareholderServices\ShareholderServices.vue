<template>
  <div class="ShareholderServices">
    ShareholderServices
    <!-- <PcContact v-if="fullWidth > 1024" />
    <MobileContact v-else /> -->
  </div>
</template>

<script>
// import PcContact from "./pc/pcContact";
// import MobileContact from "./mobile/mobileContact";
export default {
  name: "Contact",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  methods: {},
  components: {
    // PcContact,
    // MobileContact
  }
};
</script>

<style scoped>
</style>
