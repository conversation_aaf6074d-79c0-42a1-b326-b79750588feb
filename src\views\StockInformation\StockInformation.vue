<template>
  <div class="StockInformation">
    <pcStockInformation v-if="fullWidth > 1024"/>
      <mobileStockInformation v-else />
    <!-- <PcManage v-if="fullWidth > 1024" />  
    <MobileManage v-else /> -->
  </div>
</template>

<script>
// import MobileManage from "./mobile/mobileManage";
// import PcManage from "./pc/pcManage";
import pcStockInformation from "@/components/pc/pcStockInformation";
import mobileStockInformation from "@/components/mobile/mobileStockInformation";

export default {
  name: "OurManagement",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth,
    };
  },
  created(){
  },
  mounted() {
    
    window.onresize = () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    };
  },
  methods: {
    
  },
  components: {
    pcStockInformation,
    mobileStockInformation
      // MobileManage,
      // PcManage
  }
};
</script>

<style scoped>
</style>
