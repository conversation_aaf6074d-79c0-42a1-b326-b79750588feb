<template>
  <div :class = "fullWidth > 1024 ? 'head88':'head44'">
    <pcHeader v-if="fullWidth > 1024" />
    <mHerder v-else />
  </div>
</template>

<script>
import pcHeader from "@/components/header/pcHeader";
import mHerder from "@/components/header/mHeader";
export default {
  name: "Header",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth,
    };
  },
  components: {
    pcHeader,
    mHerder
  },
  mounted() {
    // 监听窗口发生变化，resize
    window.addEventListener("resize", this.fullWidthFunc);
    // 通过hook监听组件销毁钩子函数，并取消监听事件
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.fullWidthFunc)
    })
  },
  methods: {
    fullWidthFunc(){
      this.fullWidth = document.documentElement.clientWidth;
    }
  },
};
</script>
<style scoped lang="less">
    .head88{
      height:0.88rem
    }
    .head44{
      height:0.44rem
    }
</style>

