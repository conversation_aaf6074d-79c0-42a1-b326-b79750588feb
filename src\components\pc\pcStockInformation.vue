<template>
    <div class="stockInformation">
        <div class="banner">
            <img src="../../assets/images/pcStockInformation.png" />
        </div>
        <div class="w">
            <div class="profile poas">STOCK QUOTE</div>
            <div class="code">{{ stockInformation.code }}</div>
            <div class="mon">${{ resData.QLastPrice }}</div>
            <div class="date">{{ date }}</div>

            <ul>
                <li>
                    <span>Change</span>
                    <span :style="{ color: colorFunc() }"
                        >{{ rateFunc() }}</span
                    >
                    <span></span>
                    <span></span
                    >
                    <!-- <span :style="{ color: colorFunc() }"
                        >{{ changeFunc() }} {{ rateFunc() }}</span
                    > -->
                    <!-- <span :style="{ color: colorFunc() }"
                        >{{ rateFunc() }}</span
                    > -->
                    <!-- <span>Today's High</span>
                    <span>{{ resData.QHighPrice }}</span> -->
                </li>
                <!-- <li>
                    <span>Volume</span>
                    <span>{{ resData.QCirculationAmount }}</span>
                    <span>Today's Low</span>
                    <span>{{ resData.QLowPrice }}</span>
                </li> -->
                <li>
                    <span>Today's Open</span>
                    <span>{{ resData.QOpeningPrice }}</span>
                    <span>52 Week High</span>
                    <span>--</span>
                </li>
                <li>
                    <span>Previous Close</span>
                    <span>{{ resData.QPreClosingPrice }}</span>
                    <span>52 Week Low</span>
                    <span>--</span>
                </li>
            </ul>

            <!-- <div class="profile poas">STOCK CHART</div> -->

            <!-- <div class="banner">
         chartsLghl
         <img src="../../assets/images/gpjx.png" >
       </div> -->
            <!-- <iframe
                src="https://api.nasdaqomx.wallst.com/advancedchart?display=mountain&amp;symbol=LGHL.OQ-LGHL&amp;scale=linear&amp;duration=1dy&amp;frequency=15min&amp;gridLine=b&amp;bgColor=ffffff&amp;lineColor=EEAF43&amp;fillcolor=EEAF43|ffffff&amp;width=100%&amp;height=500&amp;bdr=0&amp;fillOpacity=75"
                width="100%"
                height="500"
                frameborder="0"
                scrolling="no"
                allowtransparency="true"
                class="nir-stock-chart"
                title="Stock Chart"
                style="width: 100%; height: 520px;"
            ></iframe> -->
            <!-- <div class="chartsLghl">
          <chartsLghl />
        </div> -->
            <div class="tips">
                <i>*</i> Data Provided by Refinitiv. Minimum 15 minutes delayed.
            </div>
        </div>
    </div>
</template>

<script>
import { stockInformation } from "@/pagesData/StockInformation/pages";
import chartsLghl from "../charts";
import axios from "axios";
export default {
    data() {
        return {
            stockInformation,
            resData: {},
            date: "",
        };
    },
    components: {
        chartsLghl,
    },
    methods: {
        async getTick() {
            // "https://ir.liongrouphl.com/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS";
            let URL = "https://qt.gtimg.cn/q=s_usLGHL";
            const { data: res } = await axios.get(URL);
            if (!res) return;
            const resd = res
                ?.replace('v_s_usLGHL="', "")
                ?.replace('";', "")
                ?.split("~");
            this.resData = {
                QLastPrice: resd[3] || 0,
                QChangeRate: resd[5] || 0,
                QChangeValue: resd[5] || 0,
                QPreClosingPrice: resd[3] || 0,
                QOpeningPrice: resd[3] || 0,
            };
            let dateNow = new Date().toDateString();
            this.date =
                dateNow.split(" ")[1] +
                " " +
                dateNow.split(" ")[2] +
                ", " +
                dateNow.split(" ")[3] +
                " PM EDT";
        },
        colorFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                return "green";
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                return "#ff583d";
            }
            return "#000";
        },
        rateFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                return (
                    this.resData &&
                    this.resData.QChangeRate &&
                    "(+" + this.resData.QChangeRate + "%)"
                );
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                return (
                    this.resData &&
                    this.resData.QChangeRate &&
                    "(" + this.resData.QChangeRate + "%)"
                );
            }
            return this.resData.QChangeRate + "%"
            // return 0;
        },
        changeFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                return (
                    this.resData &&
                    this.resData.QChangeValue &&
                    "+" + this.resData.QChangeValue
                );
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                return this.resData && this.resData.QChangeValue;
            }
            return 0;
        },
    },
    created() {
        this.getTick();
    },
};
</script>

<style lang="less" scoped>
.stockInformation {
    background: #fff;
    .profile {
        font-size: 0.4rem;
        font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        line-height: 0.52rem;
        letter-spacing: 0.02rem;
        margin-top: 0.96rem;
        margin-bottom: 0.8rem;
        position: relative;
    }
    .code {
        font-size: 0.22rem;
        font-family: NotoSansHans-Bold, NotoSansHans;
        font-weight: bold;
        color: rgba(34, 34, 34, 1);
        line-height: 0.33rem;
        letter-spacing: 0.01rem;
        margin-bottom: 0.34rem;
    }
    .mon {
        font-size: 0.7rem;
        font-family: NotoSansHans-Bold, NotoSansHans;
        font-weight: bold;
        color: rgba(34, 34, 34, 1);
        line-height: 1.05rem;
        letter-spacing: 0.04rem;
        margin-bottom: 0.22rem;
    }
    .date {
        font-size: 0.2rem;
        font-family: NotoSansHans-Regular, NotoSansHans;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        line-height: 0.3rem;
        letter-spacing: 0.01rem;
        margin-bottom: 0.38rem;
        text-transform: uppercase;
    }
    ul {
        margin-bottom: 1.17rem;
        li {
            height: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 0.3rem;
            span {
                flex: 1;
                font-size: 0.22rem;
                font-family: NotoSansHans-Regular, NotoSansHans;
                font-weight: 400;
                color: rgba(34, 34, 34, 1);
                line-height: 0.33rem;
                letter-spacing: 0.01rem;
                &:nth-child(2) {
                    box-sizing: border-box;
                    padding-right: 2rem;
                    text-align: end;
                    font-weight: bold;
                }
                &:nth-child(4) {
                    text-align: end;
                    font-weight: bold;
                }
            }
            &:nth-child(odd) {
                background: #f6f6f6;
            }
        }
    }
    .now {
        margin-top: 0.29rem;
        font-size: 0.2rem;
        font-family: NotoSansHans-Bold, NotoSansHans;
        font-weight: bold;
        color: rgba(34, 34, 34, 1);
        line-height: 0.3rem;
        letter-spacing: 0.01rem;
        span {
            color: #bbbbbb;
        }
    }
    .refvided {
        margin-top: 0.18rem;
        padding-bottom: 1rem;
        font-size: 0.16rem;
        font-family: NotoSansHans-Regular, NotoSansHans;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        line-height: 0.24rem;
    }
}

.tips {
    font-size: 0.14rem;
    // padding-left: 0.4rem;
    padding-bottom: 0.5rem;
    padding-top: -0.2rem;
    i {
        color: red;
        font-weight: 800;
    }
}
.poas::before {
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width: 0.6rem;
    height: 0.06rem;
    background: rgba(236, 177, 67, 1);
}
.chartsLghl {
    width: 100%;
    height: 500px;
}
</style>
