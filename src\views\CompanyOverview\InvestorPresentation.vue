<template>
	<div class="investorPre">
		<div class="banner">
            <div class="textBox">
                <div class="textBoxText">
                    INVESTOR PRESENTATION
                </div>
                <div class="textBoxPdf" @click="toPdfFunc">
                    <img class="padImg" :src="pdfImg" alt="" />
                    <span class="padName"> LGHL Investor Presentation</span>
                    <img class="downloadImg" :src="downloadImg" alt="" />
                </div>
            </div>
		</div>
	</div>
</template>

<script>
export default {
	name: "investorPre",
	data() {
		return {
            fullWidth: document.documentElement.clientWidth,
            pdfImg: require("@/assets/images/pdfImg.png"),
            downloadImg: require("@/assets/images/downloadImg.png"),
		};
    },
    methods: {
        toPdfFunc(){
            // let _url='https://image-lion.oss-cn-hongkong.aliyuncs.com/'
             // let item = 'LGHLInvestorPresentation2022.1.11'
            let _url = 'https://ir.liongrouphl.com/'
            let item = 'LGHL_InvestorDeck'
            window.open(
                `${_url}${item}.pdf`,
                "_blank"
            );
        }
    },
};
</script>

<style lang="less" scoped>
.investorPre{
    .banner{
        position: relative;
        background:url("../../assets/images/bannerIp.png") no-repeat center;
        background-size: cover;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        .textBox{
            width: 12rem;
            height: 4rem;
            &Text{
                font-size: 50px;
                color: #fff;
                font-weight: 700;
                margin: 0.7rem auto 0.36rem;
                position: relative;
                &::before {
                    content: "";
                    width: 0.6rem;
                    height: 0.06rem;
                    background-color: #ECB143;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
            }
            .textBoxPdf{
                overflow: hidden;
                border: 1px solid #555;
                border-radius: 5px;
                padding: 0.18rem 0.44rem;
                cursor: pointer;
                .padImg{
                    float: left;
                    width: 0.32rem;
                    height: 0.32rem;
                }
                .padName{
                    float: left;
                    max-width: 6rem;
                    padding-left: 0.4rem;
                    height: 0.32rem;
                    line-height: 0.32rem;
                    font-size: 0.16rem;
                    color: #fff;
                    letter-spacing: 0.02rem;
                    font-weight: 600;
                }
                .downloadImg{
                    float: right;
                    width: 0.32rem;
                    height: 0.32rem;
                }
            }
        }
    }
}
@media screen and (max-width: 1023px) {
    .investorPre{
        .banner{
            height:3rem !important;
        }
        .textBox{
            width: auto !important;
            height: 3rem !important;
            margin: 0.1rem;
            &Text{
                padding: 0.1rem;
                font-size: 0.24rem !important;
                color: #fff;
                font-weight: 700;
                margin: 0.6rem auto 0.36rem !important;
                position: relative;
                &::before {
                    content: "";
                    width: 0.6rem;
                    height: 0.06rem;
                    background-color: #ECB143;
                    position: absolute;
                    top: 0;
                    left: 0.1rem !important; 
                }
            }
            .textBoxPdf{
                overflow: hidden;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 0.12rem 0.2rem !important;
                cursor: pointer;
                .padImg{
                    float: left;
                    width: 0.24rem !important;
                    height: 0.24rem !important;
                }
                .padName{
                    float: left;
                    max-width: 6rem;
                    padding-left: 0.1rem !important;
                    height: 0.24rem !important;
                    line-height: 0.24rem !important;
                    font-size: 0.14rem !important;
                    color: #fff;
                    letter-spacing: 0.02rem;
                }
                .downloadImg{
                    float: right;
                    width: 0.24rem !important;
                    height: 0.24rem !important;
                    margin-left: 0.1rem;
                }
            }
        }
    }
}
@media screen and ( min-width: 1024px) and (max-width: 1440px) {
    .investorPre{
        .banner{
            height:6.2rem;
        }
    }
}
@media screen and ( min-width: 1441px) and (max-width: 1920px) {
    .investorPre{
        .banner{
            height:6.8rem;
        }
    }
}
</style>
