<template>
    <div class="pcHeader userSelectNo">
        <div class="center">
            <div class="imgBox">
                <img
                    class="imgLogo"
                    src="../../assets/images/logo.png"
                    alt=""
                />
                <img
                    class="imgLogoTitle"
                    src="../../assets/images/logoTitle.png"
                    alt=""
                />
            </div>
            <div class="linkItem">
                <Dropdown class="active" v-for="(items,key) in linkItem" :key="key">
                    <span :class=" items.meta.includes($route.path) ?  'tab_span colorMark' : 'tab_span'" @click="handleClick(items.path)" style="display:block">
                        {{ items.name }}
                    </span>
                    <DropdownMenu slot="list" v-for="(item,index) in items.children" :key="index">
                        <router-link :to="item.path" v-if="item.path">
                            <DropdownItem>
                                <span class="tab_span">{{ item.name }}</span>
                            </DropdownItem>
                        </router-link>
                        <span class="tab_span" @click="secFunc" v-else>
                            <DropdownItem>
                                {{ item.name }}
                            </DropdownItem>
                        </span>
                    </DropdownMenu>
                </Dropdown>
            </div>
        </div>
    </div>
</template>

<script>
import { linkItems } from '@/utils/linkItem.js';
export default {
    name: "pcHeader",
    data() {
        return {
            linkItem: linkItems
        }
    },
    mounted() {
        
    },
    methods: {
        // 一级跳转
        handleClick(data){
            if(data === '/Overview' || data === '/StockInformation'){
                this.$router.push(data)
            }
        },
        // to SEC filling
        secFunc(){
            window.open(
                "https://www.sec.gov/cgi-bin/browse-edgar?action=getcompany&CIK=0001806524&owner=exclude&count=40",
                "_blank"
            );
        }
    },
};
</script>
<style></style>
<style scoped lang="less">
@import "../../assets/css/color";
.pcHeader {
    height: 0.88rem;
    width: 100%;
    background: @themeBlack;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    .center {
        width: 12rem;
        margin: 0 auto;
        .imgBox {
            float: left;
            width: 2.4rem;
            height: 0.55rem;
            margin-top: 18px;
            .imgLogo {
                float: left;
                width: 0.52rem;
                height: 0.49rem;
            }
            .imgLogoTitle {
                float: left;
                width: 1.5rem;
                height: 0.39rem;
                margin: 8px 0 0 12px;
            }
        }
        .linkItem {
            overflow: hidden;
            height: 0.88rem;
            display: flex;
            flex-flow: nowrap;
            span {
                &:after {
                    display: block;
                    content: attr(name);
                    font-weight: bold;
                    visibility: hidden;
                    overflow: hidden;
                    height: 0.01rem;
                    font-size: 16px;
                }
            }
            & .router-link-active {
                .tab_span {
                    font-weight: bold;
                    color: @fontColorThree;
                }
            }
            .common {
                line-height: 0.88rem;
                padding: 0 0.15rem;
                cursor: pointer;
                font-size: 0.15rem;
            }
            .active {
                .common;
                color: @fontColorTwo;
            }
            .active:hover {
                color: #fff9ee;
            }
            .colorMark{
                color: @fontColorThree;
            }
        }
    }
}
@media screen and (min-width: 1025px) and (max-width: 1199px) {
    .pcHeader{
        .center{
            .linkItem{
                .common{
                    font-size: 0.13rem;
                }
                .active {
                    font-size: 0.13rem;
                }
            }
        }
    }
}
</style>
