<template>
    <div class="Overview">
        <pcOverview v-if="fullWidth > 1024" :resData="resData" />
        <mobileOverview :resData="resData" v-else />
    </div>
</template>

<script>
import pcOverview from "@/components/pc/pcOverview";
import mobileOverview from "@/components/mobile/mobileOverview";
import axios from "axios";
export default {
    name: "Overview",
    data() {
        return {
            fullWidth: document.documentElement.clientWidth,
            resData: {},
        };
    },
    mounted() {
        this.getTick();
        window.addEventListener("resize", () => {
            return (() => {
                this.fullWidth = document.documentElement.clientWidth;
            })();
        });
    },
    methods: {
        async getTick() {
            let URL =
                "https://qt.gtimg.cn/q=s_usLGHL";
            // if(process.env.VUE_APP_MODE=="dev"){
            //   URL = 'http://**********:50088/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS'
            // }else if(process.env.VUE_APP_MODE=="prod"){
            //   URL = 'https://ir.liongrouphl.com/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS'
            // }
            const { data: res } = await axios.get(URL);
            if (!res) return;
            const resd = res
                ?.replace('v_s_usLGHL="', "")
                ?.replace('";', "")
                ?.split("~");
            this.resData = {
                QLastPrice: resd[3] || 0,
                QChangeRate: resd[5] || 0,
                QChangeValue: resd[5] || 0,
                QPreClosingPrice: resd[3] || 0,
            };
            // (res.this.resData = {
            //     QLastPrice: res.data[0]?.QLastPrice,
            //     QChangeRate: res.data[0]?.QChangeRate,
            //     QChangeValue: res.data[0]?.QChangeValue,
            //     QPreClosingPrice: res.data[0]?.QPreClosingPrice,
            // });
        },
    },
    components: {
        pcOverview,
        mobileOverview,
    },
};
</script>
<style lang="less" scope></style>
