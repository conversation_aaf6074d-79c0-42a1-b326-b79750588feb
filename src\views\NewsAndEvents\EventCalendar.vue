<template>
  <div class="eventCalendar">
    <div class="banner">
      <img
        class="bannerImg"
        :src="fullWidth > 1024 ? bannerNews : bannerNewsM"
      />
    </div>
    <div class="details">
      <p class="detailsTitle">Events</p>
      <Row class="detailsRow">Events</Row>
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList5" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('meeting_2023')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
      <Row class="detailsRow">Events</Row>
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList4" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('meeting_2023')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
      <Row class="detailsRow">Events</Row>
      <!-- <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList1" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">
            <p>{{ item.tVal }}</p>
            <p>{{ item.tVal1 }}</p>
          </Col>
        </Row>
        <div class="video_box">
          <video ref="videoPlayer" class="video-js">
            <source
              src="https://img-oss-ali.lionfintechs.com/ir-video.mp4"
              
            />
          </video>
        </div>
      </div> -->
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList3" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('meeting_202309')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
      <Row class="detailsRow">Events</Row>
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList1" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('meeting_2023')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
      <Row class="detailsRow">Events</Row>
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('meeting')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
      <Row class="detailsRow">Events</Row>
      <div class="detailsListWrap">
        <Row class="detailsList" v-for="item in detailList2" :key="item.id">
          <Col span="6" class="detailsList-l">{{ item.tName }}</Col>
          <Col span="18" class="detailsList-r">{{ item.tVal }}</Col>
        </Row>
        <Row class="detailsList">
          <Col span="6"> </Col>
          <Col span="18">
            <!-- <div class="annex" @click="handleClick('Meeting')">Meeting Annex</div> -->
          </Col>
        </Row>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth,
      bannerNews: require("@/assets/images/bannerNews.png"),
      bannerNewsM: require("@/assets/images/bannerNewsM.png"),
      detailList: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "2022 Annual Shareholders’ Meeting on December 23, 2022, at 10:00 a.m. local time at 3 Phillip Street, #15-04 Royal Group Building, Singapore 048693",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "November 18, 2022",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "December 16, 2022 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "December 23, 2022",
        },
      ],
      detailList1: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "General Meeting at 10:00 AM local time at 3 Phillip Street, #15-04 Royal Group Building, Singapore 048693",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "November 18, 2022",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "January 06, 2023 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "January 13, 2023",
        },
      ],
      detailList3: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "2023 Annual Shareholders’ Meeting on October 6, 2023, at 10:00 a.m. local time at 3 Phillip Street, #15-04 Royal Group Building, Singapore 048693",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "September 8, 2023",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "September 27, 2023 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "October 6, 2023",
        },
      ],
      detailList2: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "2021 Annual General Meeting at 10:00 AM local time at Unit A-C, 33/F, Tower A, Billion Center, 1 Wang Kwong Road, Kowloon Bay, Hong Kong",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "September 10, 2021",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "October 07, 2021 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "October 15, 2021",
        },
      ],
      detailList4: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "2024 Annual General Meeting at 10:00 a.m., local time at 3 Phillip Street, #15-04 Royal Group Building, Singapore 048693",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "November 22, 2024",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "December 16, 2024 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "December 23, 2024",
        },
      ],
      detailList5: [
        {
          id: "1",
          tName: "Meeting Details",
          tVal:
            "2025 Extraordinary General Meeting at 10:00 a.m., local time at 3 Phillip Street, #15-04 Royal Group Building, Singapore 048693",
        },
        {
          id: "2",
          tName: "ADS Record Date",
          tVal: "February 12, 2025",
        },
        {
          id: "3",
          tName: "Voting Deadline",
          tVal: "March 04, 2025 at 10:00 AM EST",
        },
        {
          id: "4",
          tName: "Meeting Date",
          tVal: "March 07, 2025",
        },
      ],

      player: null,
    };
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    };

    // 播放参数
    // let options = {
    //   controls: true, // 是否显示底部控制栏
    //   preload: "auto", // 加载<video>标签后是否加载视频
    //   autoplay: "muted", // 静音播放
    //   // playbackRates: [0.5, 1, 1.5, 2],// 倍速播放
    //   controlBar: {
    //     // 自定义按钮的位置
    //     children: [
    //       {
    //         name: "playToggle",
    //       },
    //       {
    //         name: "progressControl",
    //       },
    //       {
    //         name: "currentTimeDisplay",
    //       },
    //       {
    //         name: "timeDivider",
    //       },
    //       {
    //         name: "durationDisplay",
    //       },

    //       {
    //         name: "volumePanel", // 音量调整方式横线条变为竖线条
    //         inline: false,
    //       },
    //       {
    //         name: "pictureInPictureToggle", //画中画播放模式
    //       },
    //       {
    //         name: "fullscreenToggle",
    //       },
    //     ],
    //   },
    // };
    // this.player = this.$video(
    //   this.$refs.videoPlayer,
    //   options,
    //   function onPlayerReady() {
    //     console.log("onPlayerReady", this);
    //   }
    // );
  },
  // beforeDestroy() {
  //   if (this.player) {
  //     this.player.dispose();
  //   }
  // },
  methods: {
    handleClick(item) {
      let _url = "https://ir.liongrouphl.com/";
      window.open(`${_url}${item}.pdf`, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.eventCalendar {
  width: 100%;

  .banner {
    &Img {
      display: block;
      width: 100%;
    }
  }

  .details {
    width: 12rem;
    margin: 0.7rem auto 0.6rem;

    &Title {
      font-size: 0.4rem;
      color: #333;
      font-weight: 700;
      position: relative;
      margin-bottom: 0.36rem;

      &::before {
        content: "";
        width: 0.6rem;
        height: 0.06rem;
        background-color: #ecb143;
        position: absolute;
        top: 0;
        left: 0;
      }
    }

    &Row {
      height: 0.68rem;
      line-height: 0.68rem;
      background-color: #fac45f;
      padding: 0 0.2rem;
      font-size: 0.18rem;
      font-weight: 700;
      color: #333;
      border-radius: 3px;
    }

    &ListWrap {
      margin: 0.2rem 0 0.3rem 0;
      padding: 0.1rem 0.1rem 0.2rem 0.1rem;
      border-radius: 6px;
      box-shadow: -1px 1px 7px 0px rgba(204, 204, 204, 0.3);

      .detailsList {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ddd;
        font-size: 18px;
        color: #333333;
        padding: 0.2rem 0.2rem;

        &-l {
          font-weight: 600;
          margin-right: 0.06rem;
        }
      }

      .video_box {
        margin-top: 0.2rem;
        width: 98%;
        height: 4.5rem;
      }

      .video_box /deep/ .vjs-big-play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .video-js {
        margin: 0 auto;
        width: 98%;
        height: 4.5rem;
      }
    }

    .annex {
      width: 100%;
      cursor: pointer;
      position: relative;
      padding-left: 0.36rem;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 0.28rem;
        height: 0.28rem;
        background: url("../../assets/images/pdf.png");
        background-size: 100% 100%;
      }
    }
  }
}

@media screen and (max-width: 1023px) {
  .eventCalendar {
    .details {
      width: auto;
      padding: 0rem 0.1rem 0.4rem;
    }
  }
}
</style>
