<template>
  <div class="FinancialInformatio">
    <pcFinancialInformatio />
    <!-- <PcAbout v-if="fullWidth > 1024" />
    <MobileAbout v-else /> -->
  </div>
</template>
<script>

import pcFinancialInformatio from "@/components/pc/pcFinancialInformatio";
export default {
  name: "",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  created() {},
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  methods: {},
  components: {
    pcFinancialInformatio
    // PcAbout,
    // MobileAbout
  },
  watch: {}
};
</script>
<style lang="less">
</style>
