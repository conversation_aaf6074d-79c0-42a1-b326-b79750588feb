<template>
  <div class="stockInformation">
    
    <div class="banner">
      <img src="../../assets/images/mobileStockInformation.png" >
    </div>
    <div class="pd">
       <div class="profile poas">STOCK QUOTE</div>
       <div class="code">{{stockInformation.code}}</div>
       <div class="mon">${{resData.QLastPrice}}</div>
       <div class="date">{{date}}</div>
       
       <ul>
         <li>
           <span>Change</span>
           <span :style="{color:colorFunc()}">{{ changeFunc() }} {{ rateFunc() }}</span>
         </li>
         <li>
           <span>Today's High</span>
           <span>{{resData.QHighPrice}}</span>
         </li>
         <li>
           <span>Volume</span>
           <span>{{resData.QCirculationAmount}}</span>
         </li>
         <li>
           <span>Today's Low</span>
           <span>{{resData.QLowPrice}}</span>
         </li>
          <li>
           <span>Today's Open</span>
           <span>{{resData.QOpeningPrice}}</span>
         </li>
         <li>
           <span>52 Week High</span>
           <span>--</span>
         </li>
         <li>
           <span>Previous Close</span>
           <span>{{resData.QPreClosingPrice}}</span>
         </li>
         <li>
           <span>52 Week Low</span>
           <span>--</span>
         </li>
       </ul>

       <div class="profile poas">STOCK CHART</div>
       
    </div>
    <iframe src="https://api.nasdaqomx.wallst.com/advancedchart?display=mountain&amp;symbol=LGHL.OQ-LGHL&amp;scale=linear&amp;duration=1dy&amp;frequency=15min&amp;gridLine=b&amp;bgColor=ffffff&amp;lineColor=EEAF43&amp;fillcolor=EEAF43|ffffff&amp;width=100%&amp;height=500&amp;bdr=0&amp;fillOpacity=75" width="100%" height="500" frameborder="0" scrolling="no" allowtransparency="true" class="nir-stock-chart" title="Stock Chart" style="width: 100%; height: 530px;"></iframe>
    <!-- <div class="pd-charts">
         <chartsLghl />
       </div> -->
       <div class="tips"><i>*</i> Data Provided by Refinitiv. Minimum 15 minutes delayed.</div>
  </div>
</template>

<script>
import { stockInformation } from "@/pagesData/StockInformation/pages";
import chartsLghl from '../charts'
import axios from 'axios';
  export default {
    data() {
      return {
        stockInformation,
        date:'',
        resData:{}
      }
    },
    components:{
      chartsLghl
    },
    methods:{
      async getTick(){
        let URL = 'https://ir.liongrouphl.com/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS'
        // console.log(process.env.VUE_APP_BASEURL);
        // if(process.env.VUE_APP_MODE=="dev"){
        //   URL = 'http://**********:50088/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS'
        // }else if(process.env.VUE_APP_MODE=="prod"){
        //   URL = 'https://ir.liongrouphl.com/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS'
        // }
        const { data:res } = await axios.get(URL)
        this.resData = res.data[0]
        let dateNow = new Date(res.data[0].TAPIDTSTAMP).toDateString()
        this.date = dateNow.split(' ')[1] +' ' + dateNow.split(' ')[2]+ ', ' +dateNow.split(' ')[3]+ ' PM EDT'
      },
      colorFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return 'green'
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return '#ff583d'
        }
        return '#000'
      },
      rateFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeRate&&'(+'+ this.resData.QChangeRate+'%)'
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeRate&&'('+ this.resData.QChangeRate+'%)'
        }
        return 0
      },
      changeFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeValue&&'+'+this.resData.QChangeValue
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeValue
        }
        return 0
      }
    },
    created(){
      this.getTick()
    }
  }
</script>

<style lang="less" scoped>
.pd{
    padding: 0 0.1rem;
  }
  .stockInformation{
    background: #fff;
    .profile{
      font-size:0.15rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.2rem;
      position: relative;
      margin-top: 0.2rem;
      margin-bottom: 0.3rem;
    }
    .code{
      font-size:0.13rem;
      font-family:NotoSansHans-Bold,NotoSansHans;
      font-weight:bold;
      color:rgba(34,34,34,1);
      line-height:0.2rem;
      margin-bottom: 0.22rem;
    }
    .mon{
      font-size:0.4rem;
      font-family:NotoSansHans-Bold,NotoSansHans;
      font-weight:bold;
      color:rgba(34,34,34,1);
      line-height:0.6rem;
      letter-spacing:0.02rem;
      margin-bottom: 0.16rem;
    }
    .date{
      font-size:0.13rem;
      font-family:NotoSansHans-Regular,NotoSansHans;
      font-weight:400;
      color:rgba(51,51,51,1);
      line-height:0.2rem;
      margin-bottom: 0.22rem;
    }
    ul{
      margin-bottom: 0.41rem;
      li{
        height: 0.6rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0.1rem;
        span{
          flex: 1;
          font-size:0.15rem;
          font-family:NotoSansHans-Regular,NotoSansHans;
          font-weight:400;
          color:rgba(34,34,34,1);
          line-height:0.23rem;
          &:nth-child(2){
            box-sizing: border-box;
            text-align: end;
            font-weight:bold;
          }
          &:nth-child(4){
            text-align: end;
          }
        }
        &:nth-child(odd){
          background: #F6F6F6;
        }
      }
    }
    .now{
      margin-top: 0.22rem;
      font-size:0.15rem;
      font-family:NotoSansHans-Bold,NotoSansHans;
      font-weight:bold;
      color:rgba(34,34,34,1);
      line-height:0.3rem;
      letter-spacing:0.01rem;
      span{
        color: #BBBBBB;
      }
    }
    .refvided{
      margin-top: 0.18rem;
      padding-bottom: 0.4rem;
      font-size:0.11rem;
      font-family:NotoSansHans-Regular,NotoSansHans;
      font-weight:400;
      color:rgba(102,102,102,1);
      line-height:0.17rem;
    }
  }
  .tips{
    padding: 0.1rem 0 0.2rem 0.1rem;
    i{
      color: red;
      font-weight: 800;
    }
  }
  .poas::before{
    position: absolute;
    left: 0;
    top: -0.04rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }
</style>