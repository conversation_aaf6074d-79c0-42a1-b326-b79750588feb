<template>
  <div class="img-svg" :style="{height: fullWidth > 1024 ? '' : h + 'px'}"  @touchstart="boxTouchstart($event)" @touchmove="boxTouchmove($event)" @touchend="boxTouchend($event)">
    <slot></slot>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fullWidth: document.documentElement.clientWidth,
        slw: 370 - 519,
        touchStartPointX: 0,
        poLeft: 0
    };
  },
  props: ['h','r'],
  mounted() {
      window.onresize = () => {
          return (() => {
              this.fullWidth = document.documentElement.clientWidth;
          })();
      };
  },
  methods: {
    boxTouchstart(e) {
      this.touchStartPointX = e.targetTouches[0].pageX - 10
    },
    boxTouchmove(e) {
        let left = e.targetTouches[0].pageX - 10 - this.touchStartPointX
        // if ((left + this.poLeft)< this.slw) {
        //     left = this.slw
        //     this.$refs['img-a'][0].style.left = left + 'px'
        //     this.poLeft = left
        //     return
        // } 
        // if ((left + this.poLeft) > 0) {
        //     left = 0
        //     this.$refs['img-a'][0].style.left = left  + 'px'
        //     this.poLeft = left
        //     return
        // }
        // this.poLeft = left
        this.poLeft = left 
        this.$emit('boxTouchmove', left + this.poLeft, this.r)
        // this.$refs['img-a'][0].style.left = left + this.poLeft  + 'px'
        // console.log(this.$refs['img-a'][0].style.left);
    },
    boxTouchend(e) {
        this.touchStartPointX = 0
    }
  }
}
</script>

<style>
.img-svg {
    margin: 20px auto 50px;
}
@media screen and (max-width: 1023px) { 
  .img-svg {
      width: 370px;
      border: 1px solid #ccc;
      overflow-y: hidden;
      position: relative;
  }
}
</style>