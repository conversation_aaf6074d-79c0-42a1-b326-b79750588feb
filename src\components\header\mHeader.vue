<template>
    <div class="mHeader">
        <header>
            <input type="checkbox" v-model="ischeckbox"   id="toggler">
            <label for="toggler" class="muetop" @click="ischeckbox == !ischeckbox">
                <div class="imgLogo">
                    <img src="../../assets/images/logo.png">
                    <img class="imgLogoTitle"src="../../assets/images/logoTitle.png"alt=""/>
                </div>
                <div class="hamburger-container">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </label>
            <div class="nav-items" @click.self="navClick">
                <ul v-if="showList">
                    <li :class="{'active':$route.path == item.path ? true : item.children.find(listItem=>listItem.path==$route.path)}"  @click="clickItems(item)" v-for="(item, index) in linkItem" :key="index">{{item.name}}</li>
                </ul>
                <ul class="nav-items-r" ref="item">
                    <li @click="back">< back</li>
                    <li :class="$route.path == item.path?'active':''"  @click="itemsRClick(item)" v-for="(item, index) in itemList.children" :key="index">{{item.name}}</li>
                </ul>
                
            </div>
        </header>

    </div>
</template>

<script>
import { linkItems } from '@/utils/linkItem.js';
export default {
    name: "mHeader",
    data() {
        return {
            rotate: false,
            linkItem: linkItems,
            itemList:[],
            showList:true,
            ischeckbox:false
        };
    },
    mounted() {},
    methods: {
        navClick(e){
          this.ischeckbox = false
        },
        itemsRClick(item){
            if(item.path==''){
                window.open(
                    "https://www.sec.gov/cgi-bin/browse-edgar?action=getcompany&CIK=0001806524&owner=exclude&count=40",
                    "_blank"
                );
            }else{
                this.$router.push(item.path)
                this.ischeckbox = false
            }
        },
        back(){
            this.showList = true
            this.$refs.item.style.left = '100vw'
        },
        clickItems(item){
            if(item.children.length == 0) {
                this.$router.push(item.path)
                this.ischeckbox = false
                return 
            }
            this.showList = false
            this.itemList = item
            this.$refs.item.style.left = 0
        },
    },
    watch: {
    },
};
</script>
<style>
.fade-enter-active {
    transition: all 0.3s ease;
}
.fade-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    transform: translateY(-3.36rem);
}
</style>
<style scoped lang="less">
@import "../../assets/css/color";
.mHeader {
    height: 0.44rem;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 200;
}

.muetop{
    width: 100vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 0.44rem;
}
.imgLogo{
    display: flex;
    align-items: center;
    height: 100%;
    img:nth-child(1){
        width: 0.32rem;
        height: 0.32rem;
    }
    img:nth-child(2){
        width: 0.91rem;
        margin-left: 0.12rem;
    }
}

header{
  background:rgba(21,25,31,1);
  height: 0.44rem;
  padding: 0 0.18rem;
}
.box{
  width: 1rem;
  height: 1rem;
  background-color: #000;
}
#toggler:checked + label .box{
  background-color: cyan;
}
#toggler{
  display: none;
}
.hamburger-container{
  display: block;
  width: 0.2rem;
  height: 0.2rem;
  position: relative;
  margin-right: 0.28rem;
//   top: 16px;
}
.hamburger-container span{
  display: block;
  height: 1px;
  background: #fff;
  position: relative;
  transition: transform .3s ease-in-out, top .3s ease-in-out .3s,right .3s ease-in-out .3s;
  top: 0;
}
.hamburger-container span:nth-child(2){
  margin-top: 0.07rem;
}
.hamburger-container span:nth-child(3){
  margin-top: 0.07rem;
}
#toggler:checked + label .hamburger-container span:nth-child(1){
  transform: rotate(45deg);
  top: 0.128rem;
  transition: top .3s ease-in-out, transform .3s ease-in-out .3s;
}
#toggler:checked + label .hamburger-container span:nth-child(3){
  transform: rotate(-45deg);
  top: -0.03rem;
  transition: top .3s ease-in-out, transform .3s ease-in-out .3s;
}
#toggler:checked + label .hamburger-container span:nth-child(2){
  right: -0.5rem;
  transform:  right .3s ease-in-out;
}
.nav-items{
//   background:rgba(21,25,31,1);
  width: 100%;
  position: absolute;
  top: 0.44rem;
  height: 100vh;
  left: 0;
  z-index: 100;
  transform: scaleY(0);
  transition: transform .3s ease-in-out,opacity .3s ease-in-out;
  transform-origin: 50% 0;
  opacity: 0;
  background: rgba(255,255,255,1);
  li{
    padding: 0.2rem 0.2rem;
    font-size:0.18rem;
    font-family:MicrosoftYaHei;
    color:rgba(220,211,194,1);
    line-height:0.24rem;
    background:rgba(21,25,31,1);
  }
}
#toggler:checked ~ .nav-items{
  transform: scaleY(1);
  opacity: 1;
  transition: transform .3s ease-in-out,opacity .3s ease-in-out;
}
.active{
    font-size:0.18rem;
    font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
    font-weight:bold;
    color:#ECB143 !important;
    line-height:0.24rem;  
}
.nav-items-r{
    position: absolute;
  left: 100vw;
  top: 0;
  z-index: 10;
  transition: all .3s;
  width: 100vw;
  background:rgba(21,25,31,1);
}
</style>
