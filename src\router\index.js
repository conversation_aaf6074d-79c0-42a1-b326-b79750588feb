import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter);
const routes = [
    // Overview
        {
            path: '/Overview',
            name: 'Overview',
            component: () => import('../views/Overview/Overview.vue')
        },
    // CompanyOverview
        {
            path: '/CompanyOverview',
            name: 'Company Overview',
            component: () => import('../views/CompanyOverview/CompanyOverview.vue')
        },
        {
            path: '/CompanyProfile',
            name: 'Company Profile',
            component: () => import('../views/CompanyOverview/CompanyProfile.vue')
        },
        {
            path: '/InvestorPresentation',
            name: 'Investor Presentation',
            component: () => import('../views/CompanyOverview/InvestorPresentation.vue')
        },
        {
            path: '/CorporateGovernance',
            name: 'Corporate Governance',
            component: () => import('../views/CompanyOverview/CorporateGovernance.vue')
        },
        {
            path: '/BoardOfDirectors',
            name: 'Board of Directors',
            component: () => import('../views/CompanyOverview/BoardOfDirectors.vue')
        },
        {
            path: '/Management',
            name: 'Management',
            component: () => import('../views/CompanyOverview/Management.vue')
        },
    // FinancialInformatio 
        {
            path: '/FinancialInformatio',
            name: 'Financial Informatio',
            component: () => import('../views/FinancialInformatio/FinancialInformatio.vue')
        },
        {
            path: '/FinancialReports',
            name: 'Financial Reports',
            component: () => import('../views/FinancialInformatio/FinancialReports.vue')
        },
    // NewsAndEvents
        {
            path: '/NewsAndEvents',
            name: 'News and Events',
            component: () => import('../views/NewsAndEvents/NewsAndEvents.vue')
        },
        {
            path: '/NewsReleases',
            name: 'News Releases',
            component: () => import('../views/NewsAndEvents/CompanyNews.vue')
        },
        {
            path: '/CompanyNewsDetail',
            name: 'Company News Detail',
            component: () => import('../views/NewsAndEvents/CompanyNewsDetail.vue')
        },
        {
            path: '/EventCalendar',
            name: 'Event Calendar',
            component: () => import('../views/NewsAndEvents/EventCalendar.vue')
        },
    // StockInformation
        {
            path: '/StockInformation',
            name: 'Stock Information',
            component: () => import('../views/StockInformation/StockInformation.vue')
        },
    // ShareholderServices
        {
            path: '/ShareholderServices',
            name: 'Shareholder Services',
            component: () => import('../views/ShareholderServices/ShareholderServices.vue')
        },
        {
            path: '/InvestorFAQs',
            name: 'Investor FAQs',
            component: () => import('../views/ShareholderServices/InvestorFAQs.vue')
        },
        {
            path: '/InformationRequest',
            name: 'Information Request',
            component: () => import('../views/ShareholderServices/InformationRequest.vue')
        },
        {
            path: '/IRContacts',
            name: 'IR Contacts',
            component: () => import('../views/ShareholderServices/IRContacts.vue')
        },
        {
            path: '*',
            redirect: '/Overview'
        },

]

const routerPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return routerPush.call(this, location).catch(error => error)
};


const router = new VueRouter({
    mode: 'hash',
    base: process.env.BASE_URL,
    routes
});

export default router
