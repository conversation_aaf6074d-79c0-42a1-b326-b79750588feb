(function (doc, win) {
    var docEl = doc.documentElement,
        resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
        recalc = function () {
            var clientWidth = docEl.clientWidth;
            if (!clientWidth) return;

            if(clientWidth>=1200){
                docEl.style.fontSize = 100 + 'px';
            }else if(clientWidth>=1024 && clientWidth <= 1199){
                docEl.style.fontSize = 85 + 'px';
            }else{
                docEl.style.fontSize = 100 * (clientWidth / 375) + 'px';
            }
        };

    if (!doc.addEventListener) return;
    win.addEventListener(resizeEvt, recalc, false);
    doc.addEventListener('DOMContentLoaded', recalc, false);
})(document, window);
