<template>
  <div class="footer">
    <div class="footer-center">
      <div class="footer-center-presentation">
        <div class="presentation">
          <p class="headline">Group Business</p>
          <a href="https://www.libkrsgroup.com/security/show.php?id=577" target="_blank">
            <p>Securities</p>
          </a>
          <a href="https://www.libkrsgroup.com/futures/show.php?id=584" target="_blank">
            <p>Futures</p>
          </a>
          <a href="https://www.libkrsgroup.com/derivative/show.php?id=592" target="_blank">
            <p>Derivatives</p>
          </a>
          <a href="https://www.libkrsgroup.com/agents/show.php?id=609" target="_blank">
            <p>Currency Exchange</p>
          </a>
        </div>
        <div class="presentation">
          <p class="headline">Contact Us</p>
          <!-- <p>
            <Icon type="ios-call" class="icon" />(852)2820 9000
          </p> -->
          <p>
            <Icon type="md-mail" class="icon" /><EMAIL>
          </p>
        </div>
        <div class="presentation">
          <p class="headline">Privacy Policy</p>
          <p>Legal Statement</p>
          <p>Privacy Statement</p>
          <p>Risk Disclosure Statement</p>
        </div>
      </div>
      <div class="footer-center-item">
        <div
          class="item"
          v-for="(item,index) in linkItem"
          :key="index"
          @click="link(item.path)"
        >{{item.name}}</div>
      </div>
      <div class="footer-center-bottom">
        <div class="left">©2020 Lion Group Holding Ltd.</div>
        <img src="../assets/images/logoFooter.png" alt />
      </div>
    </div>
  </div>
</template>

<script>
import { linkItemFooter } from '@/utils/linkItem.js';
export default {
  name: "Footer",
  data() {
    return {
      linkItem: linkItemFooter
    };
  },
  components: {},
  mounted() {},
  methods: {
    link(item) {
      let _toUrl;
      if(item === '/InvestorRelations'){
        _toUrl = 'https://ir.liongrouphl.com';
      }else{
        _toUrl = 'https://www.liongrouphl.com/#'+item;
      }
      window.open(
          _toUrl,
          "_blank"
        );
    }
  }
};
</script>
<style scoped lang="less">
@import "../assets/css/color";
@media screen and (min-width: 1024px) {
  .footer {
    width: 100%;
    background: #000000;
    &-center {
      width: 12rem;
      margin: 0 auto;
      font-family: Helvetica;
      &-presentation {
        border-bottom: solid 1px #3b3e42;
        display: flex;
        justify-content: space-between;
        padding: 0.2rem 0;
        .presentation {
          width: 2rem;
          .headline {
            font-size: 0.18rem;
            color: #ccc;
            line-height: 0.5rem;
          }
          p {
            cursor: pointer;
            font-size: 0.14rem;
            color: @fontColorOne;
            line-height: 0.3rem;
            &:hover {
                color: #DCDCDC;
            }
            .icon {
              font-size: 0.16rem;
              margin-right: 0.03rem;
            }
          }
        }
      }
      &-item {
        border-bottom: solid 1px #3b3e42;
        display: flex;
        justify-content: space-between;
        .item {
          font-size: 0.16rem;
          font-weight: bold;
          color: @fontColorOne;
          padding: 0.4rem 0;
          cursor: pointer;
          &:hover {
            color: #fff9ee;
          }
        }
      }
      &-bottom {
        height: 1rem;
        display: flex;
        justify-content: space-between;
        .left {
          line-height: 1rem;
          color: @fontColorOne;
          font-family: NotoSansHans-Regular, NotoSansHans;
        }
        img {
          height: 0.55rem;
          width: 2.45rem;
          margin-top: 0.27rem;
        }
      }
    }
  }
}
@media screen and (max-width: 1023px) {
  .footer {
    width: 100%;
    background: #000000;
    &-center {
      padding: 0.2rem;
      &-presentation {
        .presentation {
          .headline {
            font-size: 0.15rem;
            line-height: 0.35rem;
            font-weight: bold;
            color: #ccc;
          }
          p {
            line-height: 0.23rem;
            font-size: 0.12rem;
            color: @fontColorOne;
            .icon {
              font-size: 0.16rem;
              margin-right: 0.03rem;
            }
          }
        }
      }
      &-item {
        border-top: solid 0.01rem #3b3e42;
        border-bottom: solid 0.01rem #3b3e42;
        display: flex;
        flex-flow: row wrap;
        padding: 0.1rem 0;
        .item {
          font-size: 0.15rem;
          line-height: 0.4rem;
          color: #ccc;
          font-weight: bold;
          display: inline-block;
          margin-right: 0.37rem;
        }
        .item:nth-child(4) {
          // margin-right: 0;
        }
      }
      &-bottom {
        text-align: center;
        .left {
          color: @fontColorOne;
          font-size: 0.12rem;
          line-height: 0.5rem;
        }
        img {
          width: 1.6rem;
          height: 0.36rem;
        }
      }
    }
  }
}
</style>
