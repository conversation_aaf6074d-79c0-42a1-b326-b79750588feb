<template>
    <div>
        <div class="banner">
            <img class="bannerImg" :src=" fullWidth > 1024 ? manageBk : manageBkM" >
        </div>
        <pcGovernance v-if="fullWidth > 1024" />
        <mobGovernance v-else />
    </div>
</template>
<script>
import pcGovernance from "@/components/pc/pcGovernance";
import mobGovernance from "@/components/mobile/mobGovernance";
export default {
    name: "CorporateGovernance",
    data() {
        return {
            fullWidth: document.documentElement.clientWidth,
            manageBk: require("@/assets/images/manageBk.png"),
            manageBkM: require("@/assets/images/manageBkM.png"),
        };
    },
    components: {
        mobGovernance,
        pcGovernance,
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.fullWidth = document.documentElement.clientWidth;
            })();
        };
    },
    methods: {},
};
</script>
<style lang="less" scoped>
.banner{
    width: 100%;
    &Img{
        display: block;
        width: 100%;
    }
}
</style>
