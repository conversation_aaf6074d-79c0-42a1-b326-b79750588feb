<template>
  <div class="IRContacts">
    <pcIRContacts v-if="fullWidth > 1024" />
    <mobileIRContacts v-else/>
  </div>
</template>

<script>
import pcIRContacts from "@/components/pc/pcIRContacts";
import mobileIRContacts from "@/components/mobile/mobileIRContacts";

export default {
  name: "IRContacts",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  components: {
    pcIRContacts,
    mobileIRContacts
  }
};
</script>
<style lang="less" scope>
</style>
