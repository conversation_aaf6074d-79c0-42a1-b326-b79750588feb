<template>
  <div class="NewsAndEvents">
    NewsAndEvents
    <!-- <PcDownload v-if="fullWidth > 1024" />
    <MobileDownload v-else /> -->
  </div>
</template>

<script>
// import PcDownload from "./pc/pcDownload";
// import MobileDownload from "./mobile/mobileDownload";
export default {
  name: "Download",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      return (this.fullWidth = document.documentElement.clientWidth);
    });
  },
  components: {
    // PcDownload,
    // MobileDownload
  },
  methods: {}
};
</script>

<style scoped>
</style>
