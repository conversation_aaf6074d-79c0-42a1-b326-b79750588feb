<template>
    <div class="pcManage">
        <div class="pcManage-text">
            <div class="pcManage-img" v-if="$route.path=='/BoardOfDirectors'">
                <p>Board Diversity Matrix</p>
                <div class="title-img">
                    <img src="@/assets/images/manger1.png" alt="">
                    <img src="@/assets/images/manger.png" alt="">
                </div>
            </div>
            <div class="pcManage-img">
                <p v-if="$route.path=='/Management'">MANAGEMENT</p>
                <p v-else-if="$route.path=='/BoardOfDirectors'">BOARD OF DIRECTORS</p>
            </div>
            <div class="team" v-for="item in ( $route.path == '/Management' ? list : boardList)" :key="item.id">
                <!-- <div class="team-img"> -->
                    <!-- <img class="team-img" :src="item.img" alt /> -->
                <!-- </div> -->
                <div class="team-baseInfo">
                    <p class="name">{{ item.name }}</p>
                    <p class="text">{{ item.text }}</p>
                    <p class="text1">{{ item.text1 }}</p>
                </div>
            </div>
        </div>
        <scroll-top></scroll-top>
    </div>
</template>
<script>
import ScrollTop from "@/components/scrollTop/scrollTop";
import { list, boardList } from "@/pagesData/CompanyOverview/manage";
export default {
    name: "pcManage",
    data() {
        return {
            list,
            boardList,
        };
    },
    components: {
        ScrollTop,
    },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/color";
.pcManage {
    &-img {
        width: 100%;
        padding-bottom: 0.8rem;
        p {
            width: 12rem;
            font-size: 50px;
            color: #333;
            font-weight: 700;
            margin: 0 auto;
            position: relative;
            &::before {
                content: "";
                width: 0.6rem;
                height: 0.06rem;
                background-color: #ECB143;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
    .title-img{
        width: 12rem;
        margin: 0 auto;
        img{
            width: 100%;
        }
    }
    &-text {
        width: 100%;
        background-color: @themeWhite;
        padding: 0.7rem 0 0.36rem;
        .team {
            width: 12rem;
            margin: 0 auto 0.6rem;
            display: flex;
            &-img {
                width: 2.3rem;
                height: 2.7rem;
                img {
                    width: 2.3rem;
                    height: 2.7rem;
                    background-color: #d8d8d8;
                    border-radius: 0.08rem;
                }
            }
            &-baseInfo {
                margin-left: 0.4rem;
                width: 9.3rem;
                .name {
                    font-size: 30px;
                    color: #e3921b;
                    font-family: NotoSansHans-Bold, NotoSansHans;
                    font-weight: bold;
                }
                .text {
                    margin: 0.2rem 0;
                    color: @fontColorFive;
                    font-size: 22px;
                    font-weight: 700;
                }
                .text1 {
                    color: @fontColorFive;
                    font-size: 18px;
                }
            }
        }
    }
}
</style>
