<template>
    <div class="faqs-list">
      <div class="faqs-list-top" :style="{background:!show?'#fff':'#FAC45F'}" @click="faqsClick" ref="listBou">
        <span>{{item.where}}</span> 
        <Icon v-if="show" type="md-arrow-dropdown" />
        <Icon v-else type="md-arrow-dropright" />
      </div>
      <transition enter-active-class="scale-in-ver-top" leave-active-class="scale-out-ver-top">
        <div class="faqs-list-bou" v-if="show">
          {{item.con}}
          <pre v-if="item.inverstor1">{{item.inverstor1}}</pre>
          <pre v-if="item.inverstor2">{{item.inverstor2}}</pre>
        </div>
      </transition>
    </div>
</template>

<script>
  export default {
    props:{
      item:Object
    },
    data() {
      return {
        show:false
      }
    },
    methods:{
      faqsClick(){
        this.show = !this.show
      }
    }
  }
</script>

<style lang="less" scoped>
  .faqs-list{
      transition: height .5s;
      padding-bottom: 1px;
      &-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        background:#fff;
        box-sizing: border-box;
        padding: 0.13rem 0.1rem;
        font-size:0.15rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(51,51,51,1);
        transition: all .3s;
        cursor: pointer;
        border-radius:2px 2px 0px 0px;
        // &:hover{
        //   background: #FAC45F !important;
        // }
      }
      &-bou{
        padding: 0.16rem 0.1rem 0.28rem;
        font-size:0.15rem;
        font-family:MicrosoftYaHei;
        color:rgba(51,51,51,1);
        line-height:0.2rem;
      }
    }
    pre{
      position: relative;
      &::after{
        content: '.';
        position: absolute;
        left: 0rem;
        top: 0.15rem;
      }
    }



</style>