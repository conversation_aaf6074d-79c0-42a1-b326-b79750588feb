<template>
  <div class="financialReports">
    <div class="banner">
      <img src="../../assets/images/mobileFinancialInformatio.png" >
    </div>
    <div class="pd">
      <div class="profile poas">Financial Reports</div>
      <div v-for="(item,index) in financialReports" :key="index+1" class="fin-con">
        <div class="title">{{item.title}}</div>
        <div class="header">
          <span>Date</span>
          <span>Title</span>
        </div>
        <div class="conter" v-for="(dataItem,dataIndex) in item.data">
          <span>{{dataItem.date}}</span>
          <span @click="pdfFunc(dataItem.title)">{{dataItem.title}}</span>
        </div>
        <div v-if="item.data.length == 0" class="comingUp">Coming up</div>
      </div>
    </div>
  </div>
</template>

<script>
import chartsLghl from '../charts'
import { financialReports } from "@/pagesData/FinancialInformatio/pages";
  export default {
    data() {
      return {
        financialReports,
      }
    },
    methods: {
      pdfFunc(item){
          // let _url = 'https://ir.liongrouphl.com/';
          // window.open(
          //     `${_url}${item}.pdf`,
          //     "_blank"
          // );
      },
    },
  }
</script>

<style lang="less" scoped>
  .pd{
    padding: 0 0.1rem;
  }
  .financialReports{
    .profile{
      position: relative;
      font-size:0.18rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.24rem;
      letter-spacing:0.01rem;
      margin-top: 0.37rem;
      margin-bottom: 0.2rem;
    }
    .title{
      font-size:0.15rem;
      font-family:MicrosoftYaHei;
      color:rgba(51,51,51,1);
      line-height:0.2rem;
      margin-bottom: 0.2rem;
    }
    .header{
      height:0.45rem;
      background:rgba(250,196,95,1);
      display: flex;
      align-items: center;
      padding: 0 0.1rem;
      span{
        flex: 1;
        font-size:0.16rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(51,51,51,1);
        &:nth-child(2){
          flex: 3;
        }
      }
    }
    .conter{
      padding: 0.2rem 0.1rem;
      // height:0.45rem;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      span{
        flex: 1;
        font-size:0.15rem;
        font-family:NotoSansHans-Regular,NotoSansHans;
        font-weight:400;
        color:rgba(51,51,51,1);
        height: 100%;
        // line-height: 0.45rem;
        position: relative;
        &:nth-child(2){
          flex: 3;
          margin-left: 0.2rem;
          padding-left: 0.2rem;
          &::after{
            content: '';
            position: absolute;
            left: -0.03rem;
            top: 0.03rem;
            width:0.16rem;
            height:0.16rem;
            background: url('../../assets/images/pdf.png');
            background-size: 100% 100%;
          }
        }
      }
    }
    .fin-con{
      margin-bottom: 0.44rem;
    }
    .comingUp{
      text-align: center;
      padding-top: 0.3rem;
      font-size: 0.18rem;
      font-weight: 800;
      color: #999;
    }
  }

  .poas::before{
    position: absolute;
    left: 0;
    top: -0.04rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }
</style>