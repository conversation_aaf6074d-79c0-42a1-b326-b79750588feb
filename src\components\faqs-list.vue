<template>
    <div class="faqs-list">
      <div class="faqs-list-top" :style="{background:!show?'#F6F6F6':'#FAC45F'}" @click="faqsClick" ref="listBou">
        <span>{{item.where}}</span> 
        <Icon v-if="show" type="md-arrow-dropdown" />
        <Icon v-else type="md-arrow-dropright" />
      </div>
      <transition enter-active-class="scale-in-ver-top" leave-active-class="scale-out-ver-top">
        <div class="faqs-list-bou" v-if="show">
          {{item.con}}
          <pre v-if="item.inverstor1">{{item.inverstor1}}</pre>
          <pre v-if="item.inverstor2">{{item.inverstor2}}</pre>
        </div>
      </transition>
    </div>
</template>

<script>
  export default {
    props:{
      item:Object
    },
    data() {
      return {
        show:false
      }
    },
    methods:{
      faqsClick(){
        this.show = !this.show
      }
    }
  }
</script>

<style lang="less" scoped>
  .faqs-list{
      transition: height .5s;
      &-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height:0.8rem;
        background:#F6F6F6;
        box-sizing: border-box;
        padding: 0 0.2rem;
        font-size:0.22rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(51,51,51,1);
        transition: all .3s;
        cursor: pointer;
        &:hover{
          background: #FAC45F !important;
        }
      }
      &-bou{
        padding: 0.36rem 0.25rem 0.4rem;
        font-size:0.22rem;
        font-family:NotoSansHans-Regular,NotoSansHans;
        font-weight:400;
        color:rgba(51,51,51,1);
        line-height:0.38rem;
      }
    }
    pre{
      position: relative;
      &::after{
        content: '.';
        position: absolute;
        left: 0rem;
        top: 0.32rem;
      }
    }



</style>