import Vue from 'vue'
import App from './App.vue'
import 'babel-polyfill'
import router from './router'
// import './utils/flexible.js' // rem处理文件

import Tool from './utils/toolpc.js' // 公关方法文件
import './utils/version.js' // 版本信息
import './assets/css/index.less'
import './assets/css/resetIview.less'
import './utils/iview.js'
import 'animate.css'
import FastClick from 'fastclick'

// 视频播放
import Video from 'video.js'
import 'video.js/dist/video-js.css'

Vue.prototype.$video = Video

// 消除移动端浏览器上的点击事件的300ms的延迟
if ('addEventListener' in document) {
    document.addEventListener('DOMContentLoaded', function () {
        FastClick.attach(document.body);
    }, false);
}

// rem适配
(function (doc, win) {
    var docEl = doc.documentElement,
        resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
        recalc = function () {
            var clientWidth = docEl.clientWidth;
            if (!clientWidth) return;
            if (clientWidth >= 1200) {
                docEl.style.fontSize = 100 + 'px';
            } else if (clientWidth >= 1024 && clientWidth <= 1199) {
                docEl.style.fontSize = 85 + 'px';
            } else {
                docEl.style.fontSize = 100 * (clientWidth / 375) + 'px';
            }
        };
    if (!doc.addEventListener) return;
    win.addEventListener(resizeEvt, recalc, false);
    doc.addEventListener('DOMContentLoaded', recalc, false);
})(document, window);

Vue.prototype.$utils = Tool;
Vue.config.productionTip = false

router.afterEach((to, from, next) => {
    window.scrollTo(0, 0);
});
new Vue({
    router,
    render: h => h(App)
}).$mount('#app')
