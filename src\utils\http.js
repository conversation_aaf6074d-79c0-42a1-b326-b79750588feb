import axios from 'axios'
import Qs from 'qs'
import Utils from './toolpc'
import { Message } from 'view-design';

const rootPath = Utils.linkUrl();
const fetch = axios.create({
    baseURL: rootPath
})

//全局请求次数和请求间隙
fetch.defaults.retry = 1
fetch.defaults.retryDelay = 5000

fetch.interceptors.response.use(response => {
    if (response.data.code == "0"){
        return response.data
    } else {
        Message({
            message:response.data.msg,
            duration: 3000
        })
    }
    // 权限认证,邮箱认证

}, err => {
    if (err.code === 'ECONNABORTED' && err.message.indexOf('timeout') !== -1) {
        Message({
            message:'请求超时，请稍后再次重试!',
            duration: 3000
        })
    }
    return Promise.reject(error)
    //请求失败
})


const http = ({ method = 'get',url, data=null}) => {
    return fetch({
        method: method,
        url: url,
        data: data
    })
}

export default http
