<template>
  <div id="charts"></div>
</template>

<script>
import echarts from "echarts";
import axios from 'axios';

export default {
  name: "App",
  data() {
    var that = this;
    return {
      data1: [],
      option: {
        animation: false,
        //坐标轴指示器
        axisPointer: {
          show: true,
          // 配置线条风格为虚线风格
          lineStyle: {
            type: "dashed",
          },
          link: [
            {
              xAxisIndex: [0, 1],
            },
            {
              xAxisIndex: [2, 3],
            },
            {
              yAxisIndex: [0, 2],
            },
            {
              yAxisIndex: [1, 3],
            },
          ],
        },
        // 悬浮框
        tooltip: {
          trigger: "axis",
          position: function(point, params, dom, rect, size) {
            var obj = {
              top: 10,
            };

            if (point[0] > size.viewSize[0] / 2) {
              obj["left"] = 70;
            } else {
              obj["right"] = 70;
            }

            return obj;
          },
          formatter: function(params) {
            var html = "";
            var x, c;

            for (var d in params) {
              if (
                params[d].seriesName == "成交量1" ||
                params[d].seriesName == "成交量2"
              ) {
                c = params[d];
              }
              if (
                params[d].seriesName == "最新1" ||
                params[d].seriesName == "最新2"
              ) {
                x = params[d];
              }
            }

            if (!c.axisValue) {
              return;
            }

            html +=
              '<div class="tooltips-item"><span class="name">Time</span><span class="value">' +
              echarts.format.formatTime(
                "MM-dd hh:mm",
                parseFloat(c.axisValue)
              ) +
              "</span></div>";
            html +=
              '<div class="tooltips-item"><span class="name">LastPrice</span><span class="value ' +
              that.colorCls(typeof x.data == "number" ? x.data : x.data[1]) +
              '">' +
              (typeof x.data == "number" ? x.data : x.data[1]) +
              "</span></div>";
            html +=
              '<div class="tooltips-item"><span class="name">Chg%</span><span class="value ' +
              that.colorCls(typeof x.data == "number" ? x.data : x.data[1]) +
              '">' +
              (
                (((typeof x.data == "number" ? x.data : x.data[1]) -
                  that.lastPrice) /
                  that.lastPrice) *
                100
              ).toFixed(2) +
              "%</span></div>";
            html +=
              '<div class="tooltips-item"><span class="name">Change</span><span class="value ' +
              that.colorCls(typeof x.data == "number" ? x.data : x.data[1]) +
              '">' +
              (
                (typeof x.data == "number" ? x.data : x.data[1]) -
                that.lastPrice
              ).toFixed(2) +
              "</span></div>";
            html +=
              '<div class="tooltips-item"><span class="name">Volume</span><span class="value">' +
              c.data[1]+
              "</span></div>";

            return html;
          },
          textStyle: {
            color: "#000",
          },
          borderWidth: 1,
          borderColor: "#ECEEF2",
          backgroundColor: "rgba(255,255,255,0.9)",
          transitionDuration: 0,
          axisPointer: {
            animation: false,
            type: "cross",
          },
        },
        // grid
        grid: [
          // 第一个grid
          {
            top: 10, // 图表的外边距
            height: 240, // 图表的高度
            left: "5%",
            width: "45%", //因为是左右各一个图表，使用百分比的方式显得更方便，
          },
          // 第二个grid，第二个图表是在第一个图表的下方，所以要把它定位到底部
          {
            top: 280, //设置上方的外边距是第一个图表的高度再加10，使用top是方便我们调整下方grid的高度
            left: "5%",
            width: "45%", // 宽度与第一个图表一个大
            height: 80,
          },
          // 第三个grid，第三个图表是在第一个图表的右方，所以要把它定位到右方
          {
            top: 10, // 图表的外边距
            left: "50%", //设置右边图表的左边距是第一个图表的大小，达到定位右边的效果
            width: "45%", // 宽度与第一个图表一个大
            height: 240,
          },
          // 第四个grid，第四个图表是在第三个图表的下方，所以要把它定位到底部
          {
            top: 280, //设置上方的外边距是第三个图表的高度再加10，使用top是方便我们调整下方grid的高度
            left: "50%", //设置右边图表的左边距是第三个图表的大小，达到定位右边的效果
            width: "45%", // 宽度与第一个图表一个大
            height: 80,
          },
        ],
        // 多个图表则会存在对个x轴y轴，所以这里的配置我们也换成数组的方式
        // x轴配置，
        xAxis: [
          // 第一个grid的x轴属性
          {
            // 告诉echarts，这个第一个grid的x轴
            gridIndex: 0,
            // 坐标轴是否留白
            boundaryGap: false,
            // x轴的刻度
            axisTick: { show: false },
            // x轴的刻度值
            axisLabel: { show: false },
            max: "dataMax",
            min: "dataMin",
            type: "time",
            axisPointer: {
              show: true,
              label: {
                show: false,
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
          },
          // 第二个grid的x轴属性
          {
            // 告诉echarts，这个第一个grid的x轴
            gridIndex: 1,
            // 坐标轴是否留白
            boundaryGap: false,
            // x轴的刻度
            axisTick: { show: false },
            max: "dataMax",
            min: "dataMin",
            type: "time",
            axisLabel: {
              fontSize: 12,
              show: true,
              color: "#888",
              formatter: function(value,index) {
                var a = echarts.format.formatTime("hh:mm", parseFloat(value));
                if(that.fullWidth>1024){
                  if (a == "00:29") {
                    return "00:30";
                  }
                  if (a == "21:30") {
                    return "        21:30";
                  }
                  return a;
                }else{
                  if (a == "22:00") {
                    return "21:30    ";
                  }
                }
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
            axisPointer: {
              show: true,
              type: "line",
              label: {
                show: true,
                fontSize: 10,
                margin: 0,
                padding: 2,
                shadowBlur: 0,
                color: "#33353C",
                formatter: function(data) {
                  return echarts.format.formatTime(
                    "hh:mm",
                    parseFloat(data.value)
                  );
                },
              },
            },
          },
          // 第三个grid的x轴属性
          {
            // 告诉echarts，这个第一个grid的x轴
            gridIndex: 2,
            // 坐标轴是否留白
            boundaryGap: false,
            // x轴的刻度
            axisTick: { show: false },
            // x轴的刻度值
            axisLabel: { show: false },
            type: "category",
            data: that.grid3DateData,
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            max: "dataMax",
            min: "dataMin",
            axisPointer: {
              show: true,
              label: {
                show: false,
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
              interval: function(index) {
                if (index == 0 || index == 1) {
                  return false;
                }

                if ((index - 1) % 30 == 0) {
                  return true;
                }
                return false;
              },
            },
          },
          // 第四个grid的x轴属性
          {
            // 告诉echarts，这个第一个grid的x轴
            gridIndex: 3,
            // 坐标轴是否留白
            boundaryGap: false,
            // x轴的刻度
            axisTick: {
              show: false,
            },
            type: "category",
            max: "dataMax",
            min: "dataMin",
            data: that.grid4DateData,
            axisLabel: {
              fontSize: 12,
              show: true,
              showMinLabel: false,
              color: "#888",
              interval: function(index) {
                if ((index - 1) % 30 == 0) {
                  return true;
                }
                return false;
              },
              formatter: function(value) {
                var a = echarts.format.formatTime("hh:mm", parseFloat(value));
                if(that.fullWidth>1024){
                  if (a == "04:00") {
                    return "04:00       ";
                  }
                  return a;
                }else{
                  if (a == "03:30") {
                    return "        04:00";
                  }
                }
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
              interval: function(index) {
                // 第一条第二条线是不需要显示的，第一条是11：30的，第一个grid已经有这条数据了，所以不需要显示
                // 第二条显示的话，在中间部分会出现2条线，所以也不要显示
                if (index == 0 || index == 1) {
                  return false;
                }
                // 这里的意思是第一条数据后，每30分钟显示一条线
                if ((index - 1) % 30 == 0) {
                  return true;
                }
                return false;
              },
            },
            axisPointer: {
              show: true,
              type: "line",
              label: {
                show: true,
                fontSize: 10,
                margin: 0,
                padding: 2,
                shadowBlur: 0,
                color: "#33353C",
                formatter: function(data) {
                  return echarts.format.formatTime(
                    "hh:mm",
                    parseFloat(data.value)
                  );
                },
              },
            },
          },
        ],
        // y轴配置
        yAxis: [
          // 第一个grid的y轴属性
          {
            // 去掉刻度值旁边的指示线
            axisTick: { show: false },
            splitNumber: 9,
            gridIndex: 0,
            interval: that.priceInterval,
            max: that.priceMax,
            min: that.priceMin,
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            axisLabel: {
              fontSize: 10,
              margin: 0,
              // y轴的数值向内显示
              align:"left",
              formatter: function(value) {
                return value.toFixed(2);
              },
              color: function(value) {
                // 中间基准线的数值为黑色
                if (parseFloat(value).toFixed(2) == that.lastPrice) {
                  return that.NORMAL_COLOR;
                }

                // 上涨区域的数字为红色
                if (value > that.lastPrice) {
                  return that.UP_COLOR;
                }

                // 下方下跌的数值为绿色
                if (value < that.lastPrice) {
                  return that.DOWN_COLOR;
                }
              },
            },
            z: 3,
            axisPointer: {
              show: true,
              type: "line",
              label: {
                show: true,
                fontSize: 10,
                margin: -44,
                padding: 2,
                shadowBlur: 0,
                color: "#33353C",
                formatter: function(data) {
                  return data.value.toFixed(2);
                },
              },
            },
          },
          // 第二个grid的y轴属性
          {
            // 去掉刻度值旁边的指示线
            axisTick: { show: false },
            splitNumber: 3,
            gridIndex: 1,
            interval: that.volumeInterval,
            max: that.volumeMax,
            min: 0,
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            axisPointer: {
              show: true,
              type: "line",
              label: {
                show: true,
                fontSize: 10,
                padding: 2,
                shadowBlur: 0,
                color: "#33353C",
                formatter: function(data) {
                  if (data.value > 1000000) {
                    return parseFloat(data.value / 1000000).toFixed(2) + "万手";
                  }
                  return data.value;
                },
              },
            },
            axisLabel: {
              align: "left",
              verticalAlign: "top",
              //设置显示坐标轴的数值为不显示
              show: true,
              fontSize: 10,
              margin: 0,
              showMaxLabel: true,
              showMinLabel: false,
              color: "#33353C",
              formatter: function(value) {
                // 格式化成月/日，只在第一个刻度显示年份
                if (value == that.volumeMax) {
                  // 方便演示
                  if (value > 1000000) {
                    value = parseFloat(value / 1000000).toFixed(2) + "万手";
                  }

                  return value;
                }
                return "";
              },
            },
          },
          // 第三个grid的y轴属性
          {
            // 去掉刻度值旁边的指示线
            axisTick: { show: false },
            splitNumber: 9,
            position:  "right",
            gridIndex: 2,
            interval: that.priceInterval,
            max: that.priceMax,
            min: that.priceMin,
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
            axisLabel: {
              fontSize: 10,
              margin: 0,
              // y轴的数值向内显示
              align:'right',
              formatter: function(value) {
                var persent = (value - that.lastPrice) / that.lastPrice;
                persent = persent < 0 ? persent * -1 : persent;
                persent = persent * 100;

                return persent.toFixed(2) + "%";
              },
              color: function(value) {
                // 中间基准线的数值为黑色
                if (parseFloat(value).toFixed(2) == that.lastPrice) {
                  return that.NORMAL_COLOR;
                }

                // 上涨区域的数字为红色
                if (value > that.lastPrice) {
                  return that.UP_COLOR;
                }

                // 下方下跌的数值为绿色
                if (value < that.lastPrice) {
                  return that.DOWN_COLOR;
                }
              },
            },
            z: 3,
            axisPointer: {
              show: true,
              type: "line",
              label: {
                show: true,
                fontSize: 10,
                margin: -34,
                padding: 2,
                shadowBlur: 0,
                color: "#33353C",
                formatter: function(data) {
                  var persent = (data.value - that.lastPrice) / that.lastPrice;
                  persent = persent < 0 ? persent * -1 : persent;
                  persent = persent * 100;

                  return persent.toFixed(2) + "%";
                },
              },
            },
          },
          // 第四个grid的y轴属性
          {
            // 去掉刻度值旁边的指示线
            axisTick: { show: false },
            splitNumber: 3,
            position: "right",
            gridIndex: 3,
            interval: that.volumeInterval,
            max: that.volumeMax,
            min: 0,
            axisLabel: {
              //设置显示坐标轴的数值为不显示
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#ECEEF2",
                type: "solid",
              },
            },
            axisPointer: {
              show: true,
              label: {
                show: false,
              },
            },
            axisLine: {
              lineStyle: {
                color: "#ECEEF2",
              },
            },
          },
        ],
        // 数据可以通过xAxisIndex，yAxisIndex属性，来指定是哪个grid的数据
        series: [
          // 第一个图表的数据
          {
            name: "最新1",
            // 平滑曲线
            smooth: true,
            // 是否显示折线上的圆点
            symbol: "none",
            // 线条颜色
            lineStyle: {
              color: "#0481F8",
              width: 1,
            },
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: that.grid1Data,
            type: "line",
            z: 3,
            areaStyle: {
              color: "#F8FAFF",
            },
          },
          // 第二个图表的数据
          {
            name: "成交量1",
            xAxisIndex: 1,
            yAxisIndex: 1,
            // 柱状图柱子宽度
            barWidth: 1,
            data: that.grid2Data,
            type: "bar",
            // 设置柱状图颜色
            itemStyle: {
              normal: {
                color: function(params) {
                  return that.volumeColor1[params.dataIndex];
                },
              },
            },
          },
          // 第三个图表的数据
          {
            name: "最新2",
            // 平滑曲线
            smooth: true,
            // 是否显示折线上的圆点
            symbol: "none",
            // 线条颜色
            lineStyle: {
              color: "#0481F8",
              width: 1,
            },
            z: 3,
            xAxisIndex: 2,
            yAxisIndex: 2,
            data: that.grid3Data,
            type: "line",
            areaStyle: {
              color: "#F8FAFF",
            },
          },
          // 第四个图表的数据
          {
            name: "成交量2",
            xAxisIndex: 3,
            yAxisIndex: 3,
            // 柱状图柱子宽度
            barWidth: 1,
            data: that.grid4Data,
            type: "bar",
            // 设置柱状图颜色
            itemStyle: {
              normal: {
                color: function(params) {
                  return that.volumeColor2[params.dataIndex];
                },
              },
            },
          },
        ],
      },
      // 第一个grid的数据（折线图）
      grid1Data: [],
      // 第二个grid的数据（柱状图）
      grid2Data: [],
      // 第三个grid数据（折线图）
      grid3Data: [],
      grid3DateData: [],
      // 第四个grid数据（柱状图）
      grid4Data: [],
      grid4DateData: [],

      volumeColor1: [],
      volumeColor2: [],
      UP_COLOR: "#E24528",
      DOWN_COLOR: "#009933",
      NORMAL_COLOR: "#33353C",
      priceMax: 0,
      priceMin: 0,
      priceInterval: 0,
      volumeMax: 0,
      volumeMin: 0,
      volumeInterval: 0,
      lastPrice: 0,
      timer:null,
      fullWidth:document.documentElement.clientWidth,
      
    };
  },
  mounted() {
    this.getTick()

  },
  beforeDestroy(){
    clearInterval(this.timer)
  },
  methods: {
    setOptions() {
      var chart = echarts.init(document.getElementById("charts"));
      // console.log(this.option);
      chart.setOption(this.option);
    },
    initData() {
     this.grid1Data = []
      this.grid2Data = []
      this.grid3Data = []
      this.grid4Data = []
      this.grid4DateData = []
      this.grid3DateData = []
      var items = this.data1
      var priceMax = this.priceMax;
      var priceMin = this.priceMin;
      var priceInterval = this.priceInterval;
      var volumeMax = this.volumeMax;
      var volumeMin = this.volumeMin;
      var volumeInterval = this.volumeInterval;
      var lastPrice = this.lastPrice;
      var UP_COLOR = this.UP_COLOR;
      var DOWN_COLOR = this.DOWN_COLOR;
      var NORMAL_COLOR = this.NORMAL_COLOR;
      var volumeColor1 = this.volumeColor1;
      var volumeColor2 = this.volumeColor2;
      var getNowTime = new Date().setHours(0, 0, 0, 0) + 30*60*1000 
      for (var i in items) {
        // 上午的数据
        if (items[i].time < getNowTime) {
          if (items[i].price > priceMax) {
            priceMax = items[i].price;
          }
          if (items[i].price < priceMin || priceMin == 0) {
            priceMin = items[i].price;
          }
          // 左上方折线图
          this.grid1Data.push([items[i].time, items[i].price]);

          if (items[i].volume > volumeMax) {
            volumeMax = items[i].volume;
          }
          if (items[i].volume < volumeMin) {
            volumeMin = items[i].volume;
          }

          if (i == 0) {
            if (items[i].price >= lastPrice) {
              volumeColor1.push(UP_COLOR);
            } else {
              volumeColor1.push(DOWN_COLOR);
            }
          } else {
            if (items[i].price >= items[i - 1].price) {
              volumeColor1.push(UP_COLOR);
            } else {
              volumeColor1.push(DOWN_COLOR);
            }
          }

          // 左下方柱状图
          this.grid2Data.push([items[i].time, items[i].volume]);
        } else {
          // 下午的数据

          if (items[i].price > priceMax) {
            priceMax = items[i].price;
          }
          if (items[i].price < priceMin || priceMin == 0) {
            priceMin = items[i].price;
          }
          if (this.grid3Data.length == 0) {
            this.grid3Data.push(items[i - 1].price);
            this.grid3DateData.push(items[i - 1].time);
          }
          // 右上方折线图
          this.grid3Data.push([items[i].time, items[i].price]);
          this.grid3DateData.push(items[i].time);

          if (items[i].volume > volumeMax) {
            volumeMax = items[i].volume;
          }
          if (items[i].volume < volumeMin) {
            volumeMin = items[i].volume;
          }

          if (items[i].price >= items[i - 1].price) {
            volumeColor2.push(UP_COLOR);
          } else {
            volumeColor2.push(DOWN_COLOR);
          }

          // 第四grid的数据先添加一条数据
          if (this.grid4Data.length == 0) {
            this.grid4Data.push(items[i - 1].volume);
            this.grid4DateData.push(items[i - 1].time);
          }
          // 右下方柱状图
          this.grid4Data.push([items[i].time, items[i].volume]);
          this.grid4DateData.push(items[i].time);
          
        }
      }
      if ((lastPrice - priceMax) * -1 > lastPrice - priceMin) {
        priceMin = lastPrice - (lastPrice - priceMax) * -1;
      } else {
        priceMax = lastPrice + (lastPrice - priceMin);
      }

      priceInterval = (priceMax - lastPrice) / 4;
      volumeInterval = volumeMax / 2;

      this.priceMax = priceMax;
      this.priceMin = priceMin;
      this.priceInterval = priceInterval;
      this.volumeMax = volumeMax;
      this.volumeMin = volumeMin;
      this.volumeInterval = volumeInterval;
      this.lastPrice = lastPrice;
      this.UP_COLOR = UP_COLOR;
      this.DOWN_COLOR = DOWN_COLOR;
      this.NORMAL_COLOR = NORMAL_COLOR;
      this.volumeColor1 = volumeColor1;
      this.volumeColor2 = volumeColor2;
      this.option.series[0].data = this.grid1Data;
      this.option.series[1].data = this.grid2Data;
      this.option.series[2].data = this.grid3Data;
      this.option.series[3].data = this.grid4Data;
      this.option.xAxis[3].data = this.grid4DateData;
      this.option.xAxis[2].data = this.grid3DateData
      this.option.series[1].itemStyle = {
        normal: {
          color: function(params) {
            return volumeColor1[params.dataIndex];
          },
        },
      }
        this.option.series[3].itemStyle = {
          normal: {
            color: function(params) {
              return volumeColor2[params.dataIndex];
            },
          },
        };
      this.option.yAxis[0].interval = priceInterval
      this.option.yAxis[1].interval = volumeInterval;
      this.option.yAxis[2].interval = priceInterval;
      this.option.yAxis[3].interval = volumeInterval;
      this.option.yAxis[0].max = priceMax;
      this.option.yAxis[1].max = volumeMax;
      this.option.yAxis[2].max = priceMax;
      this.option.yAxis[3].max = volumeMax;

      this.option.yAxis[0].min = priceMin;
      this.option.yAxis[2].min = priceMin;
      this.setOptions();
    },
    colorCls(num) {
      if (num > this.lastPrice) {
        return "red";
      } else if (num == this.lastPrice) {
        return "";
      }
      return "green";
    },
    async getItems(){
        this.data1 = []
        const { data:res } = await axios.get('https://ir.liongrouphl.com/HqQueryApi/GetCurrentFenShi?businType=6&contractNo=LGHL_XNAS')
        let list = res.data[0].strkline.split('+')
        if(list[list.length -1] == ''){
          list.pop()
        }
        let nowList = list.map((item,index)=>{
          const arrNowStr = item.split('|')
          return {
            volume:Number(arrNowStr[2]),
            price:Number(item.split('|')[1]),
            avgPrice:Number(item.split('|')[0]),
            time:this.getTime(arrNowStr[arrNowStr.length-1])
          }
        })
        this.data1 = nowList
        this.initData();
      },
      getTime(str){
        var newStr =  str.slice(0,4) + '-' + str.slice(4,6) + '-' + str.slice(6,8)+ ' ' +str.slice(8,10)+':'+str.slice(10,12)+':'+str.slice(12,14)
        return Math.round(new Date(newStr))
      },
    async getTick(){
        const { data:res } = await axios.get('https://ir.liongrouphl.com/HqQueryApi/GetTick?businType=6&contractNo=LGHL_XNAS')
        this.lastPrice = res.data[0].QPreClosingPrice
        this.getItems()
        this.timer = setInterval(() => {
          this.getItems()
        }, 20000);
    }
  },
  created() {
    
  },
};
</script>

<style lang="less">
#charts {
  // max-width: 12rem;
  width: 100%;
  height: 3.9rem;
}
.tooltips-item {
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  color: #33333c;
  font-size: 0.1rem;
  width: 1.2rem;
}
.green {
  color: #009933;
}
.red {
  color: #e24528;
}
/deep/ canvas{
  left: -0.5rem;
}
</style>
