<template>
  <div class="newsDetail">
    <div class="banner">
      <img
        class="bannerImg"
        :src="fullWidth > 1024 ? bannerNews : bannerNewsM"
      />
    </div>
    <div class="newsDetailBox">
      <div class="newsDetailTitle">
        {{ newsData.title }}
      </div>
      <div class="newsDetailTime">
        {{ newsData.dateTime }}
      </div>
      <div
        class="newsDetailTitle"
        v-if="newsData.newDataNow"
        style="color: #e3921b; font-size: 18px"
      >
        {{ newsData.newDataNow.title }}
      </div>
      <div class="newsDetailTime" style="font-style: italic">
        {{ newsData.newDataNow.text1 }}
      </div>
      <div class="newsDetailTime" style="font-style: italic">
        {{ newsData.newDataNow.text2 }}
      </div>

      <div class="ulchildren" v-if="newsData.newDataNow">
        <ul>
          <li
            v-for="(val, index) in newsData.newDataNow.children"
            :key="index + 111"
          >
            {{ val }}
          </li>
        </ul>
      </div>
      <div
        class="newsDetailCon"
        v-for="(item, index) in newsData.content"
        :key="index"
      >
        <div v-html="item.text"></div>
      </div>
      <!-- 视频 -->
      <div v-show="id == 28">
        <div class="video_box">
          <video ref="videoPlayer" class="video-js">
            <source src="https://img-oss-ali.lionfintechs.com/ir-video.mp4" />
          </video>
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div
      class="newsDetailBox"
      ref="newsDetailBox"
      v-for="(item, index) in newsData.listItem"
      :key="index + 100"
    >
      <div class="newsDetailTitle" style="color: #e3921b; font-size: 18px">
        {{ item.title }}
      </div>
      <div class="newsDetailCon" v-if="item.text">
        <div v-html="item.text"></div>
      </div>
      <div class="newsDetailCon" v-if="item.text1">
        <div v-html="item.text1"></div>
      </div>
      <div class="newsDetailCon" v-if="item.text2">
        <div v-html="item.text2"></div>
      </div>
      <div class="newsDetailCon" v-if="item.text3">
        <div v-html="item.text3"></div>
      </div>
      <div class="newsDetailCon" v-if="item.text4">
        <div v-html="item.text4"></div>
      </div>
      <div class="ulchildren" v-if="item.children">
        <ul>
          <li v-for="(val, index) in item.children" :key="index + 111">
            {{ val }}
          </li>
          <div v-if="item.flag == 1" class="table-img">
            <img src="../../assets/images/table1.jpg" alt="" />
          </div>
          <div v-if="item.flag == 2" class="table-img">
            <img src="../../assets/images/tablecard1.png" alt="" />
          </div>
          <div v-if="item.flag == 3" class="table-img">
            <img src="../../assets/images/tableimg1.png" alt="" />
          </div>
          <div v-if="item.flag == 4" class="table-img">
            <img src="../../assets/images/we_4.png" alt="" />
          </div>
          <div v-if="item.flag == 2024043001" class="table-img">
            <img src="../../assets/images/2024043001.png" alt="" />
          </div>
        </ul>
      </div>
      <div class="ulchildren" v-if="item.flag">
        <ul>
          <li v-for="(val, index) in item.temp" :key="index + 111">
            <span class="title-wrap">{{ val.title }}</span>
            <div>{{ val.content }}</div>
            <div></div>
          </li>
        </ul>
      </div>
      <template v-if="item.tableImg">
        <svgimg @boxTouchmove="boxTouchmove" h="200">
          <img src="../../assets/svg/1.svg" class="img-a" ref="img-a" alt="" />
        </svgimg>
      </template>
      <template v-if="item.tableImg2">
        <svgimg @boxTouchmove="boxTouchmove" h="200">
          <img src="../../assets/svg/7.svg" class="img-a" ref="img-a" alt="" />
        </svgimg>
      </template>

      <!-- <div class="img-svg" v-if="item.tableImg" @touchstart="boxTouchstart($event)" @touchmove="boxTouchmove($event)" @touchend="boxTouchend($event)">
                
            </div> -->
    </div>
    <div class="line" v-if="newsData.newList.length"></div>
    <div
      class="newsDetailBox"
      v-for="(item, index) in newsData.newList"
      :key="index + 1000"
    >
      <div
        class="newsDetailTitle"
        style="color: #e3921b; font-size: 18px"
        v-if="item.title"
      >
        {{ item.title }}
      </div>
      <div class="newsDetailCon" v-if="item.text1">
        <div v-html="item.text1"></div>
      </div>
      <!-- <div class="newsDetailCon" v-if="item.text2">
        <div v-html="item.text2"></div>
      </div> -->
      <div class="newsDetailCon" v-if="item.text3">
        <div v-html="item.text3"></div>
      </div>
    </div>
    <div
      class="newsDetailBox"
      v-if="$route.query.id == '15' && fullWidth < 1024"
    >
      <svgimg @boxTouchmove="boxTouchmove2" r="img-b2" h="800">
        <img class="img-b img-a" ref="img-b2" src="../../assets/svg/2.svg" />
      </svgimg>
      <svgimg @boxTouchmove="boxTouchmove2" r="img-b3" h="800">
        <img class="img-b img-a" ref="img-b3" src="../../assets/svg/3.svg" />
      </svgimg>
      <svgimg @boxTouchmove="boxTouchmove2" r="img-b4" h="500">
        <img class="img-b img-a" ref="img-b4" src="../../assets/svg/4.svg" />
      </svgimg>
      <svgimg @boxTouchmove="boxTouchmove2" r="img-b5" h="800">
        <img class="img-b img-a" ref="img-b5" src="../../assets/svg/5.svg" />
      </svgimg>
      <!-- <svgimg @boxTouchmove="boxTouchmove2" r="img-b6"  h="200"> 
                <img class="img-b img-a" ref="img-b6" src="../../assets/svg/6.svg" >
            </svgimg> -->
    </div>
    <div
      class="newsDetailBox"
      v-if="$route.query.id == '15' && fullWidth > 1024"
    >
      <img class="img-b img-a" ref="img-b2" src="../../assets/svg/2.svg" />
      <img class="img-b img-a" ref="img-b3" src="../../assets/svg/3.svg" />
      <img class="img-b img-a" ref="img-b4" src="../../assets/svg/4.svg" />
      <img class="img-b img-a" ref="img-b5" src="../../assets/svg/5.svg" />
    </div>
    <div class="newsDetailBox table-title-wrap" v-if="id == 29">
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED CONSOLIDATED STATEMENTS OF OPERATIONS AND COMPREHENSIVE INCOME
        <div>(LOSS)</div>
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/table4.png" class="img2" />
      <div class="aaa">
        (i) Share and per share data have been retroactively restated to give
        effect to the reverse recapitalization
      </div>

      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED CONSOLIDATED BALANCE SHEETS
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/table2.png" class="img1" />
      <img src="../../assets/images/table3.png" class="img2" />
      <div class="aaa">
        (i) Par value of ordinary shares, additional paid-in capital and share
        data have been retroactively restated to give effect to the reverse
        recapitalization.
      </div>
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED SUMMARY OF CONDENSED CONSOLIDATED STATEMENT OF CASH FLOWS DATA
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/table5.png" class="img1" />
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED RECONCILIATIONS OF NON-GAAP AND GAAP FINANCIAL RESULTS
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/table6.png" class="img1" />
      <!-- </div> -->
    </div>

    <div class="newsDetailBox table-title-wrap" v-if="id == 33">
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED CONSOLIDATED STATEMENTS OF OPERATIONS AND COMPREHENSIVE INCOME
        <div>(LOSS)</div>
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/table_card2.png" class="img2" />
      <!--  -->
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED CONSOLIDATED BALANCE SHEETS
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/tablecard3.png" class="img2" />
      <!--  -->
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED SUMMARY OF CONDENSED CONSOLIDATED STATEMENT OF CASH FLOWS DATA
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/tablecard4.png" class="img2" />
      <!--  -->
      <div class="table-title">LION GROUP HOLDING LTD</div>
      <div class="table-title2 table-title">
        UNAUDITED RECONCILIATIONS OF NON-GAAP AND GAAP FINANCIAL RESULTS
      </div>
      <div class="table-title">(in dollar amount)</div>
      <img src="../../assets/images/tablecard5.png" class="img2" />
    </div>

    <div class="newsDetailBox table-title-wrap" v-if="id == 36">
      <div class="table-title">Lion Group Holding Limited</div>
      <div class="table-title2 table-title">
        Consolidated Statements of Operations and Comprehensive Income(LOSS)
      </div>
      <div class="table-title">(in dollar amount)</div>
      <div class="table-img">
        <img src="../../assets/images/tableimg2.png" style="width:75%" />
      </div>
      <!--  -->
      <div class="table-title" style="margin-top:20px">
        Lion Group Holding Limited
      </div>
      <div class="table-title2 table-title">Consolidated Balance Sheets</div>
      <div class="table-title">(in dollar amount)</div>
      <div class="table-img">
        <img src="../../assets/images/tableimg3.png" style="width:75%" />
        <img src="../../assets/images/tableimg4.png" style="width:75%" />
        <img src="../../assets/images/tableimg8.png" style="width:75%" />
      </div>
      <!--  -->
      <div class="table-title" style="margin-top:20px">
        Lion Group Holding Limited
      </div>
      <div class="table-title2 table-title">
        Summary of Condensed Consolidated Statement of Cash Flows Data
      </div>
      <div class="table-title">(in dollar amount)</div>
      <div class="table-img">
        <img src="../../assets/images/tableimg5.png" style="width:75%" />
      </div>
      <!--  -->
      <div class="table-title" style="margin-top:20px">
        Lion Group Holding Limited
      </div>
      <div class="table-title2 table-title">
        Reconciliations of Non-GAAP and GAAP Financial Results
      </div>
      <div class="table-title">(in dollar amount)</div>
      <div class="table-img">
        <img src="../../assets/images/tableimg6.png" style="width:75%" />
        <img
          src="../../assets/images/tableimg7.png"
          style="margin-top:20px;width:75%"
        />
      </div>
    </div>

    <div class="newsDetailBox table-title-wrap" v-if="id == 39">
      <div class="table-img">
        <img src="../../assets/images/we_5.jpg" style="width:100%" />
        <img src="../../assets/images/we_6.jpg" style="width:100%" />
        <img src="../../assets/images/we_7.jpg" style="width:100%" />
      </div>
    </div>
    <div class="newsDetailBox table-title-wrap" v-if="id == 45">
      <div class="table-img">
        <img src="../../assets/images/2024043002.jpg" style="width:100%" />
        <img src="../../assets/images/2024043003.jpg" style="width:100%" />
        <img src="../../assets/images/2024043004.jpg" style="width:100%" />
        <img src="../../assets/images/2024043005.jpg" style="width:100%" />
        <img src="../../assets/images/2024043006.jpg" style="width:100%" />
      </div>
    </div>
  </div>
</template>

<script>
import dataObj from "@/pagesData/NewsAndEvents/pages.js";
import svgimg from "./svgimg";
export default {
  components: {
    svgimg,
  },
  name: "newsDetail",
  data() {
    return {
      newsData: "",
      fullWidth: document.documentElement.clientWidth,
      bannerNews: require("@/assets/images/bannerNews.png"),
      bannerNewsM: require("@/assets/images/bannerNewsM.png"),
      player: null,
      id: "",
    };
  },
  created() {
    this.newsData = dataObj[this.$route.query.id];
    this.id = this.$route.query.id;
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    };

    // 播放参数
    let options = {
      controls: true, // 是否显示底部控制栏
      preload: "auto", // 加载<video>标签后是否加载视频
      autoplay: "muted", // 静音播放
      // playbackRates: [0.5, 1, 1.5, 2],// 倍速播放
      controlBar: {
        // 自定义按钮的位置
        children: [
          {
            name: "playToggle",
          },
          {
            name: "progressControl",
          },
          {
            name: "currentTimeDisplay",
          },
          {
            name: "timeDivider",
          },
          {
            name: "durationDisplay",
          },

          {
            name: "volumePanel", // 音量调整方式横线条变为竖线条
            inline: false,
          },
          {
            name: "pictureInPictureToggle", //画中画播放模式
          },
          {
            name: "fullscreenToggle",
          },
        ],
      },
    };
    this.player = this.$video(
      this.$refs.videoPlayer,
      options,
      function onPlayerReady() {
        // console.log("onPlayerReady", this);
      }
    );
  },
  beforeDestroy() {
    if (this.player) {
      this.player.dispose();
    }
  },
  methods: {
    boxTouchmove(e) {
      this.$refs["img-a"][0].style.left = e + "px";
    },
    boxTouchmove2(e, r) {
      this.$refs[r].style.left = e + "px";
    },
  },
};
</script>

<style lang="less" scoped>
.newsDetail {
  .banner {
    width: 100%;
    margin-bottom: 0.16rem;

    &Img {
      display: block;
      width: 100%;
    }
  }

  &Box {
    max-width: 12rem;
    margin: 0.1rem auto 0;
  }

  &Title {
    font-size: 0.3rem;
    color: #222;
    font-weight: 600;
  }

  &Time {
    font-size: 0.16rem;
    color: #666;
    padding: 0.16rem 0;
  }

  &Con {
    font-size: 0.18rem;
    color: #333;
    padding: 0.12rem 0 0.16rem;
  }

  .line {
    max-width: 12rem;
    margin: 0.1rem auto 0.2rem;
    height: 1px;
    background-color: #e9e9e9;
  }

  .ulchildren {
    padding-left: 0.5rem;

    li {
      list-style-type: disc;
      font-size: 0.18rem;
    }
  }
}

.img-a {
  width: 100%;
  height: 1005;
}

@media screen and (max-width: 1023px) {
  .newsDetail {
    &Box {
      padding: 0 0.1rem;
    }

    &Title {
      font-size: 0.2rem;
    }

    &Time {
      font-size: 0.16rem;
      padding: 0.12rem 0;
    }

    &Con {
      font-size: 0.14rem;
      padding: 0.1rem 0;
    }

    .line {
      margin: 0.1rem;
      padding: 0 0.1rem;
    }

    .ulchildren {
      padding-left: 0.2rem;

      li {
        list-style-type: disc;
        font-size: 0.14rem;
      }
    }
  }

  .img-a {
    width: 500px;
    height: 300px;
  }

  .img-b {
    width: 600px;
  }

  .img-a {
    position: absolute;
    top: 0;
    left: 0;
    width: 519px;
    height: 100%;
  }
}

.video_box {
  margin-top: 0.2rem;
  width: 98%;
  height: 4.5rem;
}

.video_box /deep/ .vjs-big-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-js {
  margin: 0 auto;
  width: 98%;
  height: 4.5rem;
}

.title-wrap {
  display: block;
  margin: 0.1rem 0;
  font-weight: 700;
}

.table-img {
  margin: 0.1rem 0;
  width: 100%;

  img {
    width: 100%;
  }
}

.table-title-wrap {
  margin-top: 0.1rem;

  text-align: center;

  .table-title {
    font-weight: 700;
    color: #520000;
  }

  .table-title2 {
    margin: 0.2rem 0;
  }

  .aaa {
    margin-bottom: 0.1rem;
  }
}
</style>
