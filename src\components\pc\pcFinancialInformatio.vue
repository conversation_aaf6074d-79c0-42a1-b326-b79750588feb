<template>
  <div class="financialReports">
    <div class="banner">
      <img src="../../assets/images/pcFinancialInformatio.png" >
    </div>
    <div class="w">
      <div class="profile poas">Financial Reports</div>
      <div v-for="(item,index) in financialReports" :key="index+1" class="fin-con">
        <div class="title">{{item.title}}</div>
        <div class="header">
          <span>Date</span>
          <span>Title</span>
        </div>
        <div class="conter" v-for="(dataItem,dataIndex) in item.data">
          <span>{{dataItem.date}}</span>
          <span  style="cursor: pointer">{{dataItem.title}}</span>
          <!-- <span @click="pdfFunc(dataItem.title)" style="cursor: pointer">{{dataItem.title}}</span> -->
        </div>
        <div v-if="item.data.length == 0" class="comingUp">Coming up</div>
      </div>
    </div>
  </div>
</template>

<script>
import { financialReports } from "@/pagesData/FinancialInformatio/pages";
  export default {
    data() {
      return {
        financialReports,
      }
    },
    methods: {
      pdfFunc(item){
          let _url = 'https://ir.liongrouphl.com/';
          window.open(
              `${_url}${item}.pdf`,
              "_blank"
          );
      },
    },
  }
</script>

<style lang="less" scoped>
  .financialReports{
    .profile{
      position: relative;
      font-size:0.4rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.52rem;
      letter-spacing:0.02rem;
      text-transform: uppercase;
      margin-top: 0.96rem;
      margin-bottom: 40px;
    }
    .title{
      font-size:0.22rem;
      font-family:NotoSansHans-Bold,NotoSansHans;
      font-weight:bold;
      color:rgba(119,119,119,1);
      line-height:0.33rem;
      margin-bottom: 0.3rem;
    }
    .header{
      height:0.68rem;
      background:rgba(250,196,95,1);
      display: flex;
      align-items: center;
      padding: 0 0.2rem;
      span{
        flex: 1;
        font-size:0.18rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(51,51,51,1);
      }
    }
    .conter{
      padding: 0 0.2rem;
      height:0.68rem;
      display: flex;
      align-items: center;
      span{
        flex: 1;
        font-size:0.18rem;
        font-family:NotoSansHans-Regular,NotoSansHans;
        font-weight:400;
        color:rgba(51,51,51,1);
        height: 100%;
        line-height: 0.68rem;
        position: relative;
        &:nth-child(2){
          margin-left: 0.4rem;
           padding-left: 0.4rem;
          &::after{
            content: '';
            position: absolute;
            left: 0;
            top: 0.17rem;
            width:0.28rem;
            height:0.28rem;
            background: url('../../assets/images/pdf.png');
            background-size: 100% 100%;
          }
        }
      }
    }
    .fin-con{
      margin-bottom: 1.6rem;
    }
  }

  .poas::before{
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width:0.6rem;
    height:0.06rem;
    background:rgba(236,177,67,1);
  }
  .comingUp{
    text-align: center;
    padding: 0.5rem 0;
    font-size: 0.18rem;
    font-weight: 800;
    color: #999;
  }
</style>