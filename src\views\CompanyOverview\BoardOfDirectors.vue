<template>
    <div>
        <div class="banner">
            <img class="bannerImg" :src=" fullWidth > 1024 ? manageBk : manageBkM" >
        </div>
        <PcManage v-if="fullWidth > 1024" />
        <MobileManage v-else />
    </div>
</template>
<script>
import PcManage from "@/components/pc/pcManage";
import MobileManage from "@/components/mobile/mobManage";
export default {
    name: "BoardOfDirectors",
    data() {
        return {
            fullWidth: document.documentElement.clientWidth,
            manageBk: require("@/assets/images/manageBk.png"),
            manageBkM: require("@/assets/images/manageBkM.png"),
        };
    },
    components: {
        MobileManage,
        PcManage
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.fullWidth = document.documentElement.clientWidth;
            })();
        };
    },
    methods: {},
};
</script>
<style lang="less" scoped>
.banner{
    width: 100%;
    &Img{
        display: block;
        width: 100%;
    }
}
</style>
