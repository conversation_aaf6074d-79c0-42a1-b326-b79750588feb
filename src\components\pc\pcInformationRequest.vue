<template>
  <div class="req">
    <div class="banner">
      <img src="../../assets/images/bannerfaqs.png" />
    </div>

    <div class="w">
      <div class="req-h poas">{{ req.h }}</div>
      <div class="req-txt">{{ req.txt }}</div>
      <div class="req-title">{{ req.title }}</div>
      <div class="req-formEd">{{ req.formEd }}(<i>*</i>).</div>
      <Form
        class="form"
        ref="formValidate"
        :model="formValidate"
        :rules="ruleValidate"
        inline
        label-position="top"
      >
        <Row :gutter="60">
          <Col span="12">
            <FormItem label="First Name" style="width:100%" prop="first_name">
              <Input v-model="formValidate.first_name" :maxlength="50"></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="Last Name" style="width:100%" prop="last_name">
              <Input v-model="formValidate.last_name" :maxlength="50"></Input>
            </FormItem>
          </Col>
        </Row>

        <Row :gutter="60">
          <Col span="12">
            <FormItem label="Email" style="width:100%" prop="email">
              <Input v-model="formValidate.email"></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="Title" style="width:100%">
              <Input v-model="formValidate.subject"></Input>
            </FormItem>
          </Col>
        </Row>

        <Row :gutter="60">
          <Col span="12">
            <FormItem label="Company" style="width:100%">
              <Input v-model="formValidate.company"></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="Investor Type" style="width:100%">
              <Select v-model="formValidate.investor_type" placeholder=" ">
                <Option value="1">Individual Investor</Option>
                <Option value="2">Buy-Side Analyst</Option>
                <Option value="3">Sell-Side Analyst</Option>
                <Option value="4">Portfolio Manager</Option>
                <Option value="5">Stock Broker</Option>
                <Option value="6">Employee</Option>
                <Option value="7">News Media</Option>
                <Option value="8">Library</Option>
                <Option value="9">Other</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="60">
          <Col span="24">
            <FormItem label="Country" style="width:100%">
              <Select
                v-model="formValidate.country"
                placeholder=" "
                @on-change="changeCountry"
              >
                <Option value="">- None -</Option>
                <Option
                  v-for="(item, index) in countryList"
                  :value="item.l_lemma_item"
                  :key="index + 1"
                  >{{ item.vc_content_en }}</Option
                >
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="60" v-if="isAddress">
          <Col span="24">
            <FormItem
              label="Street address"
              prop="street_address"
              style="width:100%"
            >
              <Input
                v-model="formValidate.street_address"
                :maxlength="150"
              ></Input>
            </FormItem>
          </Col>
        </Row>

        <Row :gutter="60">
          <Col span="8">
            <FormItem label="Work Phone" style="width:100%">
              <Input v-model="formValidate.work_phone"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="Fax" style="width:100%">
              <Input v-model="formValidate.fax"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="Home Phone" style="width:100%">
              <Input v-model="formValidate.home_phone"></Input>
            </FormItem>
          </Col>
        </Row>

        <Row :gutter="60">
          <Col span="24">
            <FormItem label="Comments" style="width:100%">
              <Input
                v-model="formValidate.comments"
                type="textarea"
                :autosize="{ minRows: 6, maxRows: 10 }"
              ></Input>
            </FormItem>
          </Col>
        </Row>

        <div class="form-code" @click="getCaptca()">
          <img style="width:100%" :src="capData.cap" />
        </div>

        <Row :gutter="60">
          <Col span="8">
            <FormItem
              label="What code is in the image? "
              style="width:100%"
              prop="cap"
            >
              <Input v-model="formValidate.cap" :maxlength="4"></Input>
            </FormItem>
          </Col>
        </Row>

        <div class="form-imgtxt">Enter the characters shown in the image.</div>
        <div class="form-get">Get new captcha!</div>

        <div class="form-submit" @click="handleSubmit('formValidate')">
          Submit
        </div>
      </Form>
    </div>
  </div>
</template>

<script>
import { req } from "@/pagesData/ShareholderServices/pages";
import mixin from '@/utils/mixin.js';
export default {
  mixins:[mixin],
  data() {
    return {
      req,
    };
  },
};
</script>

<style lang="less" scoped>
.req {
  background: #fff;
  height: 100%;
  padding-bottom: 1rem;
  &-h {
    position: relative;
    font-size: 0.4rem;
    font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
    font-weight: bold;
    color: rgba(51, 51, 51, 1);
    line-height: 0.52rem;
    letter-spacing: 0.02rem;
    margin-top: 0.8rem;
    margin-bottom: 0.6rem;
    text-transform: uppercase;
  }
  &-txt {
    font-size: 0.16rem;
    font-family: NotoSansHans-Regular, NotoSansHans;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 0.24rem;
  }
  &-title {
    font-size: 0.24rem;
    font-family: NotoSansHans-Bold, NotoSansHans;
    font-weight: bold;
    color: rgba(51, 51, 51, 1);
    line-height: 0.36rem;
    margin-top: 0.3rem;
  }
  &-formEd {
    font-size: 0.14rem;
    font-family: NotoSansHans-Regular, NotoSansHans;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 0.21rem;
    margin-top: 0.16rem;
    i {
      color: #e34656;
      font-weight: 700;
    }
  }
}

.poas::before {
  position: absolute;
  left: 0;
  top: -0.1rem;
  content: "";
  width: 0.6rem;
  height: 0.06rem;
  background: rgba(236, 177, 67, 1);
}
.form {
  margin-top: 0.5rem;
  &-code {
    width: 2.3rem;
    height: 0.9rem;
    background: rgba(246, 246, 246, 1);
    margin-bottom: 0.3rem;
  }
  &-imgtxt {
    font-size: 0.14rem;
    font-family: NotoSansHans-Regular, NotoSansHans;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 0.21rem;
  }
  &-get {
    font-size: 0.14rem;
    font-family: NotoSansHans-Regular, NotoSansHans;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 0.21rem;
    margin-top: 0.5rem;
  }
  &-submit {
    margin-top: 0.15rem;
    width: 2.5rem;
    height: 0.5rem;
    line-height: 0.5rem;
    text-align: center;
    background: rgba(236, 177, 67, 1);
    outline: none;
    cursor: pointer;
    border: none;
    font-size: 0.16rem;
    font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
    font-weight: bold;
    color: rgba(85, 58, 8, 1);
    transition: all 0.3s;
    &:hover {
      background: rgba(221, 162, 53, 1);
    }
  }
}
/deep/ .ivu-form-item-label {
  font-size: 0.16rem;
  font-family: NotoSansHans-Bold, NotoSansHans;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  line-height: 0.24rem;
}
/deep/ .ivu-form-item-error .ivu-input {
  border: 0.01rem solid #e34656 !important;
}
/deep/ .ivu-form-item-required .ivu-form-item-label:before {
  display: none;
}
/deep/ .ivu-form-item-required .ivu-form-item-label::after {
  content: "*";
  display: inline-block;
  margin-left: 0.04rem;
  line-height: 1;
  font-family: SimSun;
  font-size: 0.14rem;
  color: #e34656;
}
/deep/ .ivu-select-selection {
  height: 42px !important;
  border: 1px solid #dddddd !important;
  line-height: 42px !important;
}
/deep/ .ivu-select-dropdown {
  background: #fff;
}
/deep/ .ivu-select-placeholder,
/deep/ .ivu-select-selected-value {
  height: 42px !important;
  line-height: 42px !important;
}
/deep/ .ivu-icon-ios-arrow-down:before {
  content: "\F33D" !important;
}
</style>
