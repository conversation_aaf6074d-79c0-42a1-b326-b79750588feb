// Corporate Governance
const numberList = [
    {
        id: 1,
        name: '<PERSON><PERSON> (<PERSON>) <PERSON>',
        audit: '',
        nomination: require("@/assets/images/cPic.png"),
        compensation: require("@/assets/images/cPic.png"),
    },
    {
        id: 2,
        name: '<PERSON>',
        audit: require("@/assets/images/cPic.png"),
        nomination: require("@/assets/images/mPic.png"),
        compensation: require("@/assets/images/mPic.png"),
    },
    {
        id: 3,
        name: '<PERSON><PERSON>',
        audit: require("@/assets/images/mPic.png"),
        nomination: '',
        compensation: '',
    },
    {
        id: 4,
        name: '<PERSON><PERSON> (<PERSON>) <PERSON> ',
        audit: '',
        nomination: require("@/assets/images/mPic.png"),
        compensation: require("@/assets/images/mPic.png"),
    },
    {
        id: 5,
        name: '<PERSON>k <PERSON>',
        audit: require("@/assets/images/mPic.png"),
        nomination: '',
        compensation: '',
    },
]

// Governance Documents
const docList = [
    {
        id: 1,
        img: require("@/assets/images/pdfImg.png"),
        text: 'Code of Business Conduct and Ethics'
    }
]

// Committee Charters
const chaList = [
    {
        id: 1,
        img: require("@/assets/images/pdfImg.png"),
        text: 'Audit Committee Charter'
    },
    {
        id: 2,
        img: require("@/assets/images/pdfImg.png"),
        text: 'Compensation Committee Charter'
    },
    {
        id: 3,
        img: require("@/assets/images/pdfImg.png"),
        text: 'Corporate Governance and Nominating Committee Charter'
    },
]
export { numberList, docList, chaList }