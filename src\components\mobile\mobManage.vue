<template>
    <div class="mManage">
        <div class="mManage-img" v-if="$route.path=='/BoardOfDirectors'">
            <p>Board Diversity Matrix</p>
            <div class="title-img">
                <img src="@/assets/images/manger1.png" alt="">
                <img src="@/assets/images/manger.png" alt="">
            </div>
        </div>
        <div class="mManage-img">
            <p v-if="$route.path=='/Management'">MANAGEMENT</p>
            <p v-else-if="$route.path=='/BoardOfDirectors'">BOARD OF DIRECTORS</p>
        </div>
        <div class="mManage-text">
            <div class="team" v-for="item in ( $route.path == '/Management' ? list : boardList)" :key="item.id">
                <!-- <p class="team-img">
                    <img :src="item.img" alt />
                </p> -->
                <p class="team-name">{{ item.name }}</p>
                <p class="team-text">{{ item.text }}</p>
                <p class="team-text1">{{ item.text1 }}</p>
            </div>
        </div>
        <scroll-top></scroll-top>
    </div>
</template>
<script>
import ScrollTop from "@/components/scrollTop/scrollTop";
import { list, boardList } from "@/pagesData/CompanyOverview/manage";
export default {
    name: "",
    data() {
        return {
            list,
            boardList
        };
    },
    components: {
        ScrollTop,
    },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/color";
.mManage {
    &-img {
        width: 100%;
        // background: url("../../assets/images/banner-manage.png") no-repeat;
        background-size: 100% 100%;
        padding: 0.33rem 0 0 0.3rem;
        p {
            font-size: 0.22rem;
            color: #333;
            font-weight: 700;
            margin: 0 auto;
            position: relative;
            &::before {
                content: "";
                width: 0.26rem;
                height: 0.03rem;
                background-color: #ecb143;
                position: absolute;
                top: -0.02rem;
                left: 0;
            }
        }
    }
    .title-img{
        width: 3rem;
        img{
            width: 100%;
        }
    }
    &-text {
        width: 100%;
        padding: 0.3rem 0.1rem 0;
        .team {
            margin-bottom: 0.5rem;
            &-img {
                width: 3rem;
                height: 3.3rem;
                margin: 0 auto;
                img {
                    width: 3rem;
                    height: 3.3rem;
                    border-radius: 0.08rem;
                    background-color: #d8d8d8;
                }
            }
            &-name {
                font-size: 0.22rem;
                color: #e3921b;
                font-family: NotoSansHans-Bold, NotoSansHans;
                font-weight: bold;
                margin-top: 0.2rem;
            }
            &-text {
                margin: 0.16rem 0 0.18rem;
                color: @fontColorFive;
                font-size: 0.16rem;
                font-weight: 700;
            }
            &-text1 {
                color: @fontColorFive;
                font-size: 0.15rem;
                font-family: NotoSansHans-Regular, NotoSansHans;
            }
        }
    }
}
</style>
