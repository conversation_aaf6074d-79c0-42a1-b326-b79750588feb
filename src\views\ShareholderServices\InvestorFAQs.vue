<template>
  <div class="InvestorFAQs">
    <pcInvestorFAQs v-if="fullWidth > 1024" />
      <mobileInvestorFAQs  v-else/>
  </div>
</template>

<script>
import pcInvestorFAQs from "@/components/pc/pcInvestorFAQs";
import mobileInvestorFAQs from "@/components/mobile/mobileInvestorFAQs";
export default {
  name: "InvestorFAQs",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  components: {
    pcInvestorFAQs,
    mobileInvestorFAQs
  }
};
</script>
<style lang="less" scope>
</style>
