<template>
  <div class="FinancialInformatio">
    <pcFinancialInformatio v-if="fullWidth > 1024" />
      <mobileFinancialInformatio v-else/>
    <!-- <PcAbout v-if="fullWidth > 1024" />
    <MobileAbout v-else /> -->
  </div>
</template>
<script>

import pcFinancialInformatio from "@/components/pc/pcFinancialInformatio";
import mobileFinancialInformatio from "@/components/mobile/mobileFinancialInformatio";
export default {
  name: "",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  created() {},
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  methods: {},
  components: {
    pcFinancialInformatio,
    mobileFinancialInformatio
    // PcAbout,
    // MobileAbout
  },
  watch: {}
};
</script>
<style lang="less">
</style>
