<template>
    <div class="companyProfile">
        <div class="banner">
            <img
                class="bannerImg"
                :src="fullWidth > 1024 ? manageBk : manageBkM"
            />
        </div>
        <section>
            <div class="companyProfileImg">
                <p>
                    {{ content.title }}
                </p>
            </div>
            <section class="companyProfileNews">
                <div class="companyProfileNewsBox" v-for="(item,key) in content.news" :key="key">
                    {{ item.text }}
                </div>
            </section>
        </section>
        <scroll-top></scroll-top>
    </div>
</template>
<script>
import ScrollTop from "@/components/scrollTop/scrollTop";
import { content } from "@/pagesData/CompanyOverview/profile";
export default {
    name: "CompanyProfile",
    data() {
        return {
            content,
            fullWidth: document.documentElement.clientWidth,
            manageBk: require("@/assets/images/manageBk.png"),
            manageBkM: require("@/assets/images/manageBkM.png"),
        };
    },
    components: {
        ScrollTop,
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.fullWidth = document.documentElement.clientWidth;
            })();
        };
    },
    methods: {},
};
</script>
<style lang="less" scoped>
.companyProfile {
    .banner {
        width: 100%;
        &Img {
            display: block;
            width: 100%;
        }
    }
    &Img {
        width: 100%;
        padding: 0.7rem 0 0.6rem;
        p {
            max-width: 12rem;
            font-size: 0.4rem;
            color: #333;
            font-weight: 700;
            margin: 0 auto;
            position: relative;
            &::before {
                content: "";
                width: 0.6rem;
                height: 0.06rem;
                background-color: #ecb143;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
    &News {
        max-width: 12rem;
        margin: 0 auto;
        &Box {
            color: #333;
            font-size: 18px;
            padding-bottom: 0.3rem;
        }
    }
}
@media screen and (max-width: 1023px) {
    .companyProfile {
        &Img {
            width: 100%;
            padding: 0.4rem 0 0.3rem;
            p {
                padding: 0 0.1rem;
                font-size: 0.28rem;
                &::before {
                    left: 0.1rem;
                }
            }
        }
        &News {
            padding: 0 0.1rem;
            &Box {
                color: #333;
                font-size: 18px;
                padding-bottom: 0.6rem;
            }
        }
    }
}
</style>
