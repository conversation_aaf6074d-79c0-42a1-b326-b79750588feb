const CurrentTime = require("dayjs")().format("YYYY-M-D HH:mm:ss");
module.exports = {
    /* 部署生产环境和开发环境下的URL：可对当前环境进行区分，baseUrl 从 Vue CLI 3.3 起已弃用，要使用publicPath */ 
    /* baseUrl: process.env.NODE_ENV === 'production' ? './' : '/' */
	// publicPath: process.env.NODE_ENV === 'production' ? '/public/' : './',

	// 基本路径
	publicPath: '/',
    /* 输出文件目录：在npm run build时，生成文件的目录名称 */
    outputDir: 'dist',
    /* 放置生成的静态资源 (js、css、img、fonts) 的 (相对于 outputDir 的) 目录 */
    // assetsDir: "assets",
    /* 是否在构建生产包时生成 sourceMap 文件，false将提高构建速度 */
    productionSourceMap: false,
    /* 默认情况下，生成的静态资源在它们的文件名中包含了 hash 以便更好的控制缓存，你可以通过将这个选项设为 false 来关闭文件名哈希。(false的时候就是让原来的文件名不改变) */
    filenameHashing: false,
    /* 代码保存时进行eslint检测 */
	lintOnSave: false,
	// css相关配置
	css: {
		// 是否使用css分离插件 ExtractTextPlugin
		extract: true,
		// 开启 CSS source maps?
		sourceMap: false,
		// css预设器配置项
		loaderOptions: {},
		// 启用 CSS modules for all css / pre-processor files.
		requireModuleExtension: true
	},
	// use thread-loader for babel & TS in production build
	// enabled by default if the machine has more than 1 cores
	parallel: require('os').cpus().length > 1,
    /* webpack-dev-server 相关配置 */
    devServer: {
        /* 自动打开浏览器 */
        open: false,
        /* 设置为0.0.0.0则所有的地址均能访问 */
        host: '0.0.0.0',
        port: 8080,
        https: false,
        hotOnly: false,
        /* 使用代理 */
        // proxy: {
        //     '/api': {
        //         /* 目标代理服务器地址 */
        //         target: 'http://***********/',
        //         /* 允许跨域 */
        //         changeOrigin: true,
        //     },
        // },
	},
	// 第三方插件配置
	pluginOptions: {
		// ...
	},
	chainWebpack: config => {
		config.plugin('define').tap( args => {
			args[0]['process.env.UPDATETIME'] = JSON.stringify(CurrentTime);    // 方式2：通过process.env.VERSION访问
			return args;
		})
	}
}