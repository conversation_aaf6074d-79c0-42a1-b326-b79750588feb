<template>
  <div class="mobileIRC">
    <div class="banner">
      <img src="../../assets/images/bannerfaqsip.png" >
    </div>
    <div class="mobileIRC-w">
      <div class="IPC-h poas">
      {{irc.h}}
      </div>
      <div class="IPC-text">
       {{irc.txt}}
      </div>

      <div class="IPC-data" v-for="(item,index) in irc.data" :key="index+1">
        <div class="IPC-data-title">{{item.title}}</div>
        <div v-if="item.title1" class="IPC-data-title">{{item.title1}}</div>
        <div class="IPC-data-con">{{item.email}}</div>
        <div class="IPC-data-con">{{item.phone}}</div>
      </div>

    </div>
  </div>
</template>

<script>
import { irc } from "@/pagesData/ShareholderServices/pages";
  export default {
    data() {
      return {
        irc,
      }
    }
  }
</script>

<style lang="less" scoped>
  .mobileIRC-w{
    padding: 0.37rem 0.1rem 0.4rem;
  }

  .IPC-h{
    position: relative;
    font-size:0.18rem;
    font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
    font-weight:bold;
    color:rgba(51,51,51,1);
    line-height:0.24rem;
    letter-spacing:0.01rem;
  }
  .IPC-data{
    padding-left: 0.29rem;
    position: relative;
    &::after{
      content: '';
      position: absolute;
      width: 0.16rem;
      height: 0.16rem;
      top: 0.04rem;
      left: 0.04rem;
      background: url('../../assets/images/iconjt.png');
      background-size:100% 100%;  
    }
  }
  .IPC-data-title{
    font-size:0.16rem;
    font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
    font-weight:bold;
    color:rgba(34,34,34,1);
    line-height:0.21rem;
    margin-bottom: 0.15rem;
  }
  .IPC-data-con{
    font-size:0.13rem;
    font-family:MicrosoftYaHei;
    color:rgba(34,34,34,1);
    line-height:0.17rem;
    margin-bottom: 0.15rem;
  }
  .IPC-text{
    font-size:0.13rem;
    font-family:MicrosoftYaHei;
    color:rgba(51,51,51,1);
    line-height:0.17rem;
    margin: 0.3rem 0 0.2rem;
  }
  .poas::before{
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }
</style>