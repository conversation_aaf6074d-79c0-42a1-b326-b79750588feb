<template>
  <div class="InformationRequest">
    <pcInformationRequest v-if="fullWidth > 1024" />
      <mobileInformationRequest v-else />
  </div>
</template>

<script>
import pcInformationRequest from "@/components/pc/pcInformationRequest";
import mobileInformationRequest from "@/components/mobile/mobileInformationRequest";
export default {
  name: "InformationRequest",
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    });
  },
  components: {
    pcInformationRequest,
    mobileInformationRequest
  }
};
</script>
<style lang="less" scope>
.InformationRequest{
  height: 100%;
}
</style>
