<template>
    <div>
        <Row class="mobGovernance-title">
            CORPORATE GOVERNANCE
        </Row>
        <Row class="mobGovernance-text">
            Below is a summary of our committee structure and membership
            information.
        </Row>
        <Row
            class="mobGovernance-list"
            v-for="(item, key) in numberList"
            :key="item.id + 10"
        >
            <Row style="paddingBottom: 10px;fontWeight: 600">
                {{ item.name }}
            </Row>
            <Row v-if="item.audit" class="rowName">
                <img :src="item.audit" alt="" class="numberPic" />
                <span class="spanText">Audit</span>
            </Row>
            <Row v-if="item.nomination" class="rowName">
                <img :src="item.nomination" alt="" class="numberPic" />
                <span class="spanText">Nomination</span>
            </Row>
            <Row v-if="item.compensation" class="rowName">
                <img :src="item.compensation" alt="" class="numberPic" />
                <span class="spanText">Compensation</span>
            </Row>
        </Row>
        <Row class="mobGovernance-sign">
            <Col span="12">
                <img class="mobGovernance-sign-img" :src="cPic" alt="" />
                <span class="mobGovernance-sign-span">= Chairperson</span>
            </Col>
            <Col span="12">
                <img class="mobGovernance-sign-img" :src="mPic" alt="" />
                <span class="mobGovernance-sign-span">= Member</span>
            </Col>
        </Row>
        <Row class="mobGovernance-doc">
            Governance Documents
        </Row>
        <div
            v-for="(item, key) in docList"
            :key="item.id + 100"
            class="mobGovernance-doc-box"
            @click="pdfFunc(item.text)"
        >
            {{ item.text }}
            <img :src="item.img" alt="" class="pdfImg" />
        </div>
        <Row class="mobGovernance-doc">
            Committee Charters
        </Row>
        <div
            v-for="(list, key) in chaList"
            :key="list.id + 200"
            class="mobGovernance-doc-box"
            @click="pdfFunc(list.text)"
        >
            {{ list.text }}
            <img :src="list.img" alt="" class="pdfImg" />
        </div>
        <scroll-top></scroll-top>
    </div>
</template>

<script>
import ScrollTop from "@/components/scrollTop/scrollTop";
import {
    numberList,
    docList,
    chaList,
} from "@/pagesData/CompanyOverview/governance";
export default {
    name: "mobGovernance",
    data() {
        return {
            numberList,
            docList,
            chaList,
            mPic: require("@/assets/images/mPic.png"),
            cPic: require("@/assets/images/cPic.png"),
        };
    },
    components: {
        ScrollTop,
    },
    methods: {
        pdfFunc(item) {
            // let _url = "https://ir.liongrouphl.com/";
            // window.open(`${_url}${item}.pdf`, "_blank");
        },
    },
};
</script>

<style lang="less" scoped>
.mobGovernance {
    &-title {
        padding: 0 0.1rem;
        font-size: 24px;
        color: #333;
        font-weight: 700;
        margin: 0.4rem auto 0.2rem;
        position: relative;
        &::before {
            content: "";
            width: 0.6rem;
            height: 0.06rem;
            background-color: #ecb143;
            position: absolute;
            top: -0.04rem;
            left: 0.1rem;
        }
    }
    &-text {
        padding: 0 0.1rem;
        font-size: 14px;
        padding-bottom: 0.1rem;
    }
    &-list {
        margin: 0.1rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 0.16rem 0.1rem;
        .rowName {
            padding: 0.06rem;
            .numberPic {
                float: left;
                width: 0.28rem;
                height: 0.28rem;
            }
            .spanText {
                float: left;
                padding-top: 0.04rem;
                padding-left: 0.08rem;
            }
        }
    }
    &-sign {
        padding: 0.1rem 0 0.3rem 0.06rem;
        &-img {
            float: left;
            width: 0.28rem;
            height: 0.28rem;
        }
        &-span {
            float: left;
            padding-top: 0.04rem;
        }
    }
    &-doc {
        margin: 0.1rem;
        height: 0.45rem;
        line-height: 0.45rem;
        background-color: #fac45f;
        padding-left: 0.1rem;
        font-weight: 700;
        color: #222;
        font-size: 0.16rem;
    }
    &-doc-box {
        margin: 0.1rem;
        padding: 0.12rem 0 0.12rem 0.24rem;
        position: relative;
        .pdfImg {
            position: absolute;
            top: 0.14rem;
            left: 0;
            display: block;
            width: 0.18rem;
            height: 0.18rem;
        }
    }
}
</style>
