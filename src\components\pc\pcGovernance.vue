<template>
    <div class="pcGovernance">
        <Row class="w">
            <Row class="pcGovernance-title">
                CORPORATE GOVERNANCE
            </Row>
            <Row class="pcGovernance-text">
                Below is a summary of our committee structure and membership information.
            </Row>
            <!-- Governance title -->
            <Row class="pcGovernance-item-title">
                <Col span="6" style="visibility:hidden;">space</Col>
                <Col span="6">Audit</Col>
                <Col span="6">Nomination</Col>
                <Col span="6">Compensation</Col>
            </Row>
            <!-- Governance number -->
            <Row class="pcGovernance-item-text" v-for="(item,key) in numberList" :key="key+10">
                <Col span="6" class="pcGovernance-item-box1" style="text-align: left">{{ item.name }}</Col>
                <Col span="6" class="pcGovernance-item-box">
                    <img class="pcGovernance-item-text-img" :src="item.audit" alt="" v-if="item.audit"/>
                    <span style="visibility:hidden;" v-else>space</span>
                </Col>
                <Col span="6" class="pcGovernance-item-box">
                    <img class="pcGovernance-item-text-img" :src="item.nomination" alt="" v-if="item.nomination"/>
                    <span style="visibility:hidden;" v-else >space</span>
                </Col>
                <Col span="6" class="pcGovernance-item-box">
                    <img class="pcGovernance-item-text-img" :src="item.compensation" alt="" v-if="item.compensation"/>
                    <span style="visibility:hidden;" v-else>space</span>
                </Col>
            </Row>

            <Row style="padding: 0.32rem 0">
                <Col span="4">
                    <img class="pcGovernance-item-text-img" :src="cPic" alt=""/> 
                    <span class="pcGovernance-item-text-span">= Chairperson</span>
                </Col>
                <Col span="4">
                    <img class="pcGovernance-item-text-img" :src="mPic" alt=""/>
                    <span class="pcGovernance-item-text-span">= Member</span>
                </Col>
            </Row>

            <Row style="marginBottom: 0.2rem">
                <Col span="11" class="pcGovernance-doc">
                    Governance Documents
                </Col>
                <Col span="11" offset="2" class="pcGovernance-cha pcGovernance-doc">
                    Committee Charters
                </Col>
            </Row>
            <Row style="paddingBottom: 0.32rem;">
                <Col span="11">
                    <div v-for="(item,key) in docList" :key="key+100" class="pcGovernance-doc-box" @click="pdfFunc(item.text)">
                        {{ item.text }}
                        <img :src="item.img" alt="" class="pdfImg"/>
                    </div>
                </Col>
                <Col span="11" offset="2">
                    <div v-for="(item,key) in chaList" :key="key+200" class="pcGovernance-cha-box pcGovernance-doc-box" @click="pdfFunc(item.text)">
                        {{ item.text }}
                        <img :src="item.img" alt="" class="pdfImg"/>
                    </div>
                </Col>
            </Row>
        </Row>
        
    </div>
</template>

<script>
    import { numberList, docList, chaList } from '@/pagesData/CompanyOverview/governance';
    export default {
        name: 'pcGovernance',
        data() {
            return {
                numberList,
                docList,
                chaList,
                mPic: require("@/assets/images/mPic.png"),
                cPic: require("@/assets/images/cPic.png"),
            }
        },
        methods: {
            pdfFunc(item){
                let _url = 'https://ir.liongrouphl.com/';
                window.open(
                    `${_url}${item}.pdf`,
                    "_blank"
                );
            },
        },
    }
</script>

<style lang="less" scoped>
.pcGovernance{
    &-title{
        width: 12rem;
        font-size: 50px;
        color: #333;
        font-weight: 700;
        margin: 0.7rem auto 0.36rem;
        position: relative;
        &::before {
            content: "";
            width: 0.6rem;
            height: 0.06rem;
            background-color: #ECB143;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    &-text{
        font-size: 16px;
        padding-bottom: 0.32rem;
    }
    &-item-title{
        height: 0.68rem;
        line-height: 0.68rem;
        background-color: #FAC45F;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
    }
    &-item-text{
        height: 0.68rem;
        line-height: 0.68rem;
        border-bottom: 1px solid #ddd;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        &-img{
            float: left;
            width: 0.32rem;
            height: 0.32rem;
        }
        &-span{
            float: left;
            height: 0.32rem;
            line-height: 0.32rem;
        }
    }
    &-item-box{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-size: 18px;
    }
    &-doc{
        height: 0.68rem;
        line-height: 0.68rem;
        padding-left: 0.1rem;
        font-weight: 600;
        font-size: 18px;
        color: #333;
        background-color: #FAC45F;
    }
    &-doc-box{
        padding: 0.12rem 0 0.12rem 0.4rem;
        position: relative;
        cursor: pointer;
        .pdfImg{
            position: absolute;
            top: 0.08rem;
            left: 0;
            display: block;
            width: 0.28rem;
            height: 0.28rem;
        }
    }
} 
</style>