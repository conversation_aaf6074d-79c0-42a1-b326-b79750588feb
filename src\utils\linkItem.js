const linkItems = [
    {
        name: 'Overview',
        meta: ['/Overview'],
        path: '/Overview',
        children: [],
    },
    {
        name: 'Company Overview',
        meta: ['/CompanyProfile','/CorporateGovernance','/BoardOfDirectors','/Management','/InvestorPresentation'],
        path: '/CompanyOverview',
        children: [
            {
                name: 'Company Profile',
                meta: 'Company Profile',
                path: '/CompanyProfile',
            },
            {
                name: 'Investor Presentation',
                meta: 'Investor Presentation',
                path: '/InvestorPresentation',
            },
            {
                name: 'Corporate Governance',
                meta: 'Corporate Governance',
                path: '/CorporateGovernance',
            },
            {
                name: 'Board of Directors',
                meta: 'Board of Directors',
                path: '/BoardOfDirectors',
            },
            {
                name: 'Management',
                meta: 'Management',
                path: '/Management',
            },
        ],
    },
    {
        name: 'Financial Information',
        meta: ['/FinancialReports'],
        path: '/FinancialInformation',
        children: [
            {
                name: 'Financial Reports',
                meta: 'Financial Reports',
                path: '/FinancialReports',
            },
            {
                name: 'SEC Filings',
                meta: 'SEC Filings',
                path: '',
            },
        ],
    },
    {
        name: 'News and Events',
        meta: ['/NewsReleases','/EventCalendar'],
        path: '/NewsAndEvents',
        children: [
            {
                name: 'News Releases',
                meta: 'News Releases',
                path: '/NewsReleases',
            },
            {
                name: 'Events',
                meta: 'Event Calendar',
                path: '/EventCalendar',
            },
        ],
    },
    {
        name: 'Stock Information',
        meta: ['/StockInformation'],
        path: '/StockInformation',
        children: [],
    },
    {
        name: 'Shareholder Services',
        meta: ['/InvestorFAQs','/InformationRequest','/IRContacts'],
        path: '/ShareholderServices',
        children: [
            {
                name: 'Investor FAQs',
                meta: 'Investor FAQs',
                path: '/InvestorFAQs',
            },
            {
                name: 'Information Request',
                meta: 'Information Request',
                path: '/InformationRequest',
            },
            {
                name: 'IR Contacts',
                meta: 'IR Contacts',
                path: '/IRContacts',
            },
        ],
    },
];

const linkItem = [
    {
        name: 'Overview',
        meta: 'Overview',
        path: '/Overview',
    },
    {
        name: 'Company Overview',
        meta: 'Company Overview',
        path: '/CompanyOverview',
    },
    {
        name: 'Financial Informatio',
        meta: 'Financial Informatio',
        path: '/FinancialInformatio',
    },
    {
        name: 'News and Events',
        meta: 'News and Events',
        path: '/NewsAndEvents',
    },
    {
        name: 'Stock Information',
        meta: 'Stock Information',
        path: '/StockInformation',
    },
    {
        name: 'Shareholder Services',
        meta: 'Shareholder Services',
        path: '/ShareholderServices',
    },
];

// 新官网链接
const linkItemFooter = [
    {
      name: "Home",
      meta: "Home",
      path: ""
    },
    {
      name: "News",
      meta: "New",
      path: "/New"
    },
    {
      name: "Business",
      meta: "Business",
      path: "/Business"
    },
    // {
    //   name: "Download",
    //   meta: "Download",
    //   path: "/Download"
    // },
    // {
    //   name: "Management",
    //   meta: "Management",
    //   path: "/Management"
    // },
    {
        name:"Investor Relations",
        meta: "InvestorRelations",
        path:"/InvestorRelations"
    },
    {
        name: "Contact",
        meta: "Contact",
        path: "/Contact"
      },
  ]

export { linkItem, linkItems, linkItemFooter };
