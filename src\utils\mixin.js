import { captcha, dicCountry,shareHolder } from "@/utils/api";
export default {
  data() {
    return {
      formValidate: {},
      ruleValidate: {
        first_name: [{ required: true, message: " ", trigger: "blur" }],
        last_name: [{ required: true, message: " ", trigger: "blur" }],
        email: [
          { required: true, message: " ", trigger: "blur" },
          {
            type: "email",
            message: "Please enter the correct email address.",
            trigger: "blur",
          },
        ],
        cap: [{ required: true, message: " ", trigger: "blur" }],
        street_address: [{ required: true, message: " ", trigger: "blur" }],
      },
      isAddress: false,
      capData: {},
      countryList: [],
    };
  },
  created() {
    this.getCaptca();
    this.getDicCountry();
  },
  methods: {
    async getCaptca() {
      const { data: res } = await captcha();
      this.capData = res;
    },
    async getDicCountry() {
      const { data: res } = await dicCountry();
      this.countryList = res;
    },
    handleSubmit(name) {
      this.$refs[name].validate(async (valid) => {
        if (!valid) return;
        const { data } =  await shareHolder( {
          ...this.formValidate,
          cap_id: this.capData.cap_id,
        });
        if (data.code == 0) {
          this.getCaptca()
          this.formValidate = {}
          this.$Message.success('Submitted successfully')
        } else{
          this.$Message.config({
              top: 100,
              duration: 3
          })
          this.$Message.error(data.msg)
          this.getCaptca()
        }
      });
    },
    changeCountry() {
      switch (this.formValidate.country) {
        case "" || undefined:
          this.isAddress = false;
          break;
        default:
          this.isAddress = true;
          break;
      }
    },
  },
};