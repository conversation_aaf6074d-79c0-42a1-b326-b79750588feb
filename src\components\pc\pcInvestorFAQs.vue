<template>
  <div class="faqs">

    <div class="banner">
      <img src="../../assets/images/bannerfaqs.png">
    </div>

    <div class="w faqs-l">
      <div class="faqs-h poas">{{faqs.title}}</div>
      <div v-for="(item,index) in faqs.data" :key="index+1">
        <faqs-list :item="item"></faqs-list>
      </div>
    </div>
  </div>
</template>

<script>
import { faqs } from "@/pagesData/ShareholderServices/pages";
import faqsList from "@/components/faqs-list.vue";
  export default {
    name: 'pcInvestorFAQs',
    data() {
      return {
        faqs,
      }
    },
    components:{
      faqsList
    },
    methods:{
    }
  }
</script>

<style lang="less" scoped>
  .faqs{
    background: #fff;
    height: 100%;
    padding-bottom: 1rem;
    &-h{
      position: relative;
      font-size:0.4rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.52rem;
      letter-spacing:0.02rem;
      margin-top: 0.8rem;
      margin-bottom: 0.6rem;
      text-transform: uppercase;
    }
  }
  .poas::before{
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width:0.6rem;
    height:0.06rem;
    background:rgba(236,177,67,1);
  }
</style>