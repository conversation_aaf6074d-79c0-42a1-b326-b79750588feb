<template>
    <div class="pcOverview">
        <div class="banner">
            <img src="../../assets/images/banneroverview.png" />
        </div>

        <div class="w">
            <div class="profile poas animate__animated animate__fadeInDown">
                {{ profile.title }}
            </div>
            <div class="center-text animate__animated animate__fadeInDown">
                {{ profile.conter }}
            </div>
            <div style="display: flex;">
                <button
                    class="profile-but animate__animated animate__fadeInDown"
                    @click="toCompanyPro"
                >
                    {{ profile.buttext }}
                </button>
            </div>
        </div>

        <div class="quote">
            <div class="w">
                <div class="quote-top animate__animated" ref="quoteTop">
                    <div class="poas">{{ quote.titleLest }}</div>
                    <div class="poas">{{ quote.titleRight }}</div>
                </div>

                <div class="quote-bon">
                    <div class="quote-bon-f animate__animated" ref="quoteBonFt">
                        <div class="quote-nasdaq">{{ quote.nasdaq }}</div>
                        <div class="quote-code">
                            <span>${{ resData && resData.QLastPrice }}</span>
                            <!-- <span :style="{ color: colorFunc() }">{{
                                changeFunc()
                            }}</span> -->
                            <span :style="{ color: colorFunc() }">{{
                                rateFunc()
                            }}</span>
                        </div>
                    </div>
                    <div
                        class="quote-bon-f animate__animated"
                        ref="quoteBonFb"
                        style="display: flex;"
                    ></div>
                    <!-- <div class="quote-bon-f animate__animated" ref="quoteBonFb" style="display: flex;" @click="toPdf(quote.grounp)">
            <img class="pdfimg" src="../../assets/images/pdf.png">
            <span class="quote-bon-r" style="cursor:pointer;">{{quote.grounp}}</span>
          </div> -->
                </div>
            </div>
        </div>

        <div class="w news">
            <div class="profile poas animate__animated" ref="newsH">
                {{ news.h }}
            </div>
            <div
                class="news-list animate__animated"
                ref="newList"
                v-for="(item, index) in news.data.slice(0, 3)"
                :key="index + 1"
                @click="toNewsDetail(item.id)"
            >
                <div class="news-date" ref="newDate">{{ item.date }}</div>
                <div class="news-title">{{ item.title }}</div>
                <div class="news-con">{{ item.con }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { profile, quote, news } from "@/pagesData/Overview/pages";
export default {
    name: "pcOverview",
    props: ["resData"],
    data() {
        return {
            profile,
            quote,
            news,
            changeRate: "",
        };
    },
    methods: {
        handleScroll() {
            let scrollTop =
                window.pageXOffset ||
                document.documentElement.scrollTop ||
                document.body.scrollTop;
            if (scrollTop >= 300 && scrollTop < 800) {
                this.$refs.quoteTop.classList.add("animate__fadeInDown");
                this.$refs.quoteBonFt.classList.add("animate__fadeInDown");
                this.$refs.quoteBonFb.classList.add("animate__fadeInDown");
            } else if (scrollTop >= 800) {
                this.$refs.newsH.classList.add("animate__fadeInDown");
                if (this.$refs.newList.length != 0) {
                    this.$refs.newList[0].classList.add("animate__fadeInDown");
                    this.$refs.newList[1].classList.add("animate__fadeInDown");
                }
            }
        },
        toCompanyPro() {
            this.$router.push("/CompanyProfile");
        },
        toNewsDetail(id) {
            this.$router.push({
                path: "/CompanyNewsDetail",
                query: {
                    id: id,
                },
            });
        },
        toPdf(item) {
            let _url = "https://ir.liongrouphl.com/";
            // let item = 'LGHL Investor Presentation';
            window.open(`${_url}${item}.pdf`, "_blank");
        },
        colorFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                return "green";
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                return "#ff583d";
            }
            return "#ccc";
        },
        rateFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                // return this.resData&&this.resData.QChangeRate&&'(+'+ this.resData.QChangeRate+'%)'
                return "+" + this.resData.QChangeRate + "%)";
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                // return this.resData&&this.resData.QChangeRate&&'('+ this.resData.QChangeRate+'%)'
                return this.resData.QChangeRate + "%";
            }
            return this.resData.QChangeRate + "%";
        },
        changeFunc() {
            if (this.resData.QLastPrice > this.resData.QPreClosingPrice) {
                return (
                    this.resData &&
                    this.resData.QChangeValue &&
                    "+" + this.resData.QChangeValue
                );
            } else if (
                this.resData.QLastPrice < this.resData.QPreClosingPrice
            ) {
                return this.resData && this.resData.QChangeValue;
            }
            return 0;
        },
    },
    mounted() {
        window.addEventListener("scroll", this.handleScroll);
    },
    beforeDestroy() {
        window.removeEventListener("scroll", this.handleScroll);
    },
};
</script>

<style lang="less" scoped>
.pcOverview {
    .banner {
        width: 100%;
        img {
            width: 100%;
        }
    }
    .profile {
        position: relative;
        font-size: 0.4rem;
        font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        line-height: 0.52rem;
        letter-spacing: 0.02rem;
        text-transform: uppercase;
        margin-top: 0.96rem;
    }
    .center-text {
        font-size: 0.2rem;
        font-family: MicrosoftYaHei;
        color: rgba(34, 34, 34, 1);
        line-height: 0.28rem;
        letter-spacing: 0.01rem;
        margin-top: 0.3rem;
    }
    .profile-but {
        height: 0.5rem;
        border: 0.01rem solid rgba(227, 146, 27, 1);
        padding: 0.12rem 0.5rem;
        background: #fff;
        margin: 0.5rem auto 1.02rem;
        font-size: 0.16rem;
        font-family: MicrosoftYaHei;
        color: rgba(227, 146, 27, 1);
        line-height: 0.27rem;
        outline: none;
        cursor: pointer;
        &:hover {
            border-color: #d37f04;
            color: #d37f04;
        }
    }
    .quote {
        background: url("../../assets/images/banner_bgquote.png");
        background-size: 100% 100%;
        height: 4.13rem;
        &-top {
            display: flex;
            div {
                position: relative;
                margin-top: 0.8rem;
                flex: 1;
                font-size: 0.4rem;
                font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
                font-weight: bold;
                color: rgba(255, 255, 255, 1);
                line-height: 0.52rem;
                letter-spacing: 0.02rem;
                text-transform: uppercase;
            }
        }
        &-bon {
            display: flex;
            &-f {
                flex: 1;
                margin-top: 0.63rem;
            }
        }
        &-nasdaq {
            font-size: 0.2rem;
            font-family: NotoSansHans-Medium, NotoSansHans;
            font-weight: 500;
            color: rgba(255, 255, 255, 1);
            line-height: 0.3rem;
            letter-spacing: 0.01rem;
        }
        &-code {
            margin-top: 0.35rem;
            span:nth-child(1) {
                font-size: 0.7rem;
                font-family: NotoSansHans-Bold, NotoSansHans;
                font-weight: bold;
                color: rgba(255, 255, 255, 1);
                line-height: 1.05rem;
                letter-spacing: 0.04rem;
                margin-right: 0.2rem;
            }
            span:nth-child(n + 2) {
                font-size: 0.3rem;
                font-family: NotoSansHans-Medium, NotoSansHans;
                font-weight: 500;
                color: rgba(255, 88, 61, 1);
                line-height: 0.45rem;
                letter-spacing: 0.01rem;
            }
            span:nth-child(3) {
                margin-left: 0.09rem;
            }
        }
    }
    .quote-bon-r {
        width: 4.5rem;
        font-size: 0.26rem;
        font-family: NotoSansHans-Bold, NotoSansHans;
        font-weight: bold;
        color: rgba(255, 255, 255, 1);
        line-height: 0.4rem;
        letter-spacing: 0.01rem;
    }
    .pdfimg {
        width: 0.28rem;
        height: 0.28rem;
        margin-right: 0.15rem;
        margin-top: 0.09rem;
    }
    .news {
        overflow: hidden;
        margin-bottom: 0.8rem;
        &-list {
            margin-top: 0.53rem;
            cursor: pointer;
        }
        &-date {
            font-size: 0.2rem;
            font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
            font-weight: bold;
            color: rgba(119, 119, 119, 1);
            line-height: 0.26rem;
            letter-spacing: 0.01rem;
            margin-bottom: 0.18rem;
        }
        &-title {
            font-size: 0.25rem;
            font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
            font-weight: bold;
            color: rgba(227, 146, 27, 1);
            line-height: 0.37rem;
            letter-spacing: 0.01rem;
            margin-bottom: 0.2rem;
            transition: all 0.3s;
            &:hover {
                color: #d7850d;
            }
        }
        &-con {
            font-size: 0.18rem;
            font-family: MicrosoftYaHei;
            color: rgba(34, 34, 34, 1);
            line-height: 0.27rem;
        }
    }
}
.poas::before {
    position: absolute;
    left: 0;
    top: -0.1rem;
    content: "";
    width: 0.6rem;
    height: 0.06rem;
    background: rgba(236, 177, 67, 1);
}
</style>
